"use client";

import React from 'react';
import { motion } from 'framer-motion';

export function WhyChooseUsSection() {
  const reasons = [
    {
      icon: "⚡",
      title: "Lightning-Fast Response",
      description: "Our 24/7 emergency response team ensures your HVAC issues are resolved within hours, not days. Advanced dispatch system guarantees rapid deployment.",
      highlight: "< 2 Hour Response",
      color: "from-amber-500 to-orange-500",
      bgColor: "from-amber-50 to-orange-50"
    },
    {
      icon: "🔧",
      title: "Advanced HVAC Technology",
      description: "Proprietary diagnostic tools and cutting-edge repair methods reduce downtime by 60% while ensuring long-lasting solutions.",
      highlight: "60% Faster Repairs",
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-50 to-cyan-50"
    },
    {
      icon: "👨‍🔧",
      title: "Certified Specialists",
      description: "EPA-certified technicians with 10+ years experience and continuous training on latest HVAC technologies and safety protocols.",
      highlight: "EPA Certified Team",
      color: "from-emerald-500 to-teal-500",
      bgColor: "from-emerald-50 to-teal-50"
    },
    {
      icon: "🛡️",
      title: "Comprehensive Warranty",
      description: "Industry-leading warranty coverage with 100% satisfaction guarantee. All parts and labor protected for complete peace of mind.",
      highlight: "100% Satisfaction",
      color: "from-purple-500 to-indigo-500",
      bgColor: "from-purple-50 to-indigo-50"
    }
  ];

  const specialties = [
    {
      icon: "🏭",
      title: "Industrial HVAC",
      description: "Manufacturing solutions",
      color: "from-gray-600 to-slate-600"
    },
    {
      icon: "🏢",
      title: "Commercial Systems",
      description: "Office & retail HVAC",
      color: "from-blue-500 to-indigo-500"
    },
    {
      icon: "⚙️",
      title: "Smart Controls",
      description: "Intelligent automation",
      color: "from-purple-500 to-indigo-500"
    },
    {
      icon: "🔧",
      title: "Maintenance",
      description: "Preventive care programs",
      color: "from-emerald-500 to-teal-500"
    },
    {
      icon: "❄️",
      title: "Air Conditioning",
      description: "Smart cooling solutions",
      color: "from-cyan-500 to-blue-500"
    },
    {
      icon: "🔥",
      title: "Heating Systems",
      description: "Efficient heating tech",
      color: "from-red-500 to-orange-500"
    }
  ];

  return (
    <section className="relative w-full py-24 bg-gradient-to-br from-slate-50 via-blue-50/40 to-slate-50 overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-400/30 to-cyan-400/30 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-purple-400/30 to-indigo-400/30 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-emerald-400/20 to-teal-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-2000"></div>
      </div>

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
        {/* Enhanced Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center gap-3 px-8 py-4 rounded-full bg-gradient-to-r from-blue-100 via-cyan-100 to-emerald-100 text-blue-800 text-sm font-bold mb-8 shadow-lg">
            <span className="w-3 h-3 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full animate-pulse"></span>
            Why Choose Our HVAC Excellence
            <span className="w-3 h-3 bg-gradient-to-r from-cyan-600 to-emerald-600 rounded-full animate-pulse delay-500"></span>
          </div>
          <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="text-gray-900">Your Trusted</span>{' '}
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-cyan-600 to-emerald-600">
              HVAC Excellence Partner
            </span>
          </h2>
          <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Experience unmatched HVAC solutions with our certified experts, cutting-edge technology,
            and commitment to your comfort and satisfaction.
          </p>
        </motion.div>

        {/* Main Reasons Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-24">
          {reasons.map((reason, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group relative bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/30 hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 overflow-hidden"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${reason.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-start gap-6 mb-6">
                  <div className={`p-4 bg-gradient-to-r ${reason.color} rounded-2xl text-white text-4xl group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg`}>
                    {reason.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors duration-300">
                      {reason.title}
                    </h3>
                    <div className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${reason.color} text-white text-sm font-bold rounded-full shadow-md`}>
                      <span className="w-2 h-2 bg-white rounded-full animate-pulse"></span>
                      {reason.highlight}
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300">
                  {reason.description}
                </p>

                {/* Decorative Element */}
                <div className={`absolute -bottom-2 -right-2 w-24 h-24 bg-gradient-to-r ${reason.color} rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-500`}></div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Specialties Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-800 text-sm font-bold mb-6">
            <span className="w-2 h-2 bg-emerald-600 rounded-full animate-pulse"></span>
            Our Core Specialties
          </div>
          <h3 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Complete <span className="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">HVAC Solutions</span>
          </h3>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            From industrial installations to smart home automation, we deliver comprehensive HVAC solutions
            tailored to your specific needs and industry requirements.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {specialties.map((specialty, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group relative text-center p-8 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/30 hover:bg-white/90 transition-all duration-500 hover:shadow-xl hover:-translate-y-2 overflow-hidden"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${specialty.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>

              {/* Content */}
              <div className="relative z-10">
                <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${specialty.color} rounded-2xl text-white text-3xl mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg`}>
                  {specialty.icon}
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors duration-300">
                  {specialty.title}
                </h4>
                <p className="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                  {specialty.description}
                </p>

                {/* Decorative Element */}
                <div className={`absolute -bottom-4 -right-4 w-20 h-20 bg-gradient-to-r ${specialty.color} rounded-full opacity-5 group-hover:opacity-15 transition-opacity duration-500`}></div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
