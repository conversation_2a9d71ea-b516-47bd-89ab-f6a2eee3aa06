{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/lib/utils.ts"], "sourcesContent": ["import { ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/ui/typewriter-effect-smooth.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport const TypewriterEffectSmooth = ({\r\n  words,\r\n  className,\r\n  cursorClassName,\r\n}: {\r\n  words: {\r\n    text: string;\r\n    className?: string;\r\n  }[];\r\n  className?: string;\r\n  cursorClassName?: string;\r\n}) => {\r\n  const [displayedText, setDisplayedText] = useState(\"\");\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  // Combine all words into one continuous text\r\n  const fullText = words.map(word => word.text).join(\" \");\r\n  const firstWordClass = words[0]?.className || \"\";\r\n\r\n  useEffect(() => {\r\n    const timeout = setTimeout(() => {\r\n      if (currentIndex < fullText.length) {\r\n        // Typing the full sentence\r\n        setDisplayedText(fullText.substring(0, currentIndex + 1));\r\n        setCurrentIndex(currentIndex + 1);\r\n      }\r\n      // Once fully typed, keep it displayed (no deleting or cycling)\r\n    }, 100); // Typing speed\r\n\r\n    return () => clearTimeout(timeout);\r\n  }, [currentIndex, fullText]);\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center gap-1\", className)}>\r\n      <div className=\"text-xs sm:text-base md:text-xl lg:text-3xl xl:text-5xl font-bold\">\r\n        <span className={cn(\"text-blue-800 font-medium italic\", firstWordClass)}>\r\n          {displayedText}\r\n        </span>\r\n        <span \r\n          className={cn(\r\n            \"inline-block w-0.5 h-4 sm:h-6 xl:h-12 bg-blue-800 ml-1 animate-pulse\",\r\n            cursorClassName\r\n          )}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKO,MAAM,yBAAyB,CAAC,EACrC,KAAK,EACL,SAAS,EACT,eAAe,EAQhB;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,6CAA6C;IAC7C,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;IACnD,MAAM,iBAAiB,KAAK,CAAC,EAAE,EAAE,aAAa;IAE9C,IAAA,kNAAS,EAAC;QACR,MAAM,UAAU,WAAW;YACzB,IAAI,eAAe,SAAS,MAAM,EAAE;gBAClC,2BAA2B;gBAC3B,iBAAiB,SAAS,SAAS,CAAC,GAAG,eAAe;gBACtD,gBAAgB,eAAe;YACjC;QACA,+DAA+D;QACjE,GAAG,MAAM,eAAe;QAExB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;KAAS;IAE3B,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,2BAA2B;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAW,IAAA,yHAAE,EAAC,oCAAoC;8BACrD;;;;;;8BAEH,8OAAC;oBACC,WAAW,IAAA,yHAAE,EACX,wEACA;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { TypewriterEffectSmooth } from \"@/components/ui/typewriter-effect-smooth\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nconst Hero: React.FC = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n  const [currentBg, setCurrentBg] = useState(0);\r\n\r\n  // Typewriter animation words\r\n  const words = [\r\n    { text: \"Breathe\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-blue-400 font-bold\" },\r\n    { text: \"Live\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-cyan-400 font-bold\" },\r\n  ];\r\n\r\n  // Background rotation\r\n  const backgrounds = [\"/background.jpg\", \"/background1.jpg\", \"/about1.jpg\"];\r\n\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n    const bgInterval = setInterval(() => {\r\n      setCurrentBg((prev) => (prev + 1) % backgrounds.length);\r\n    }, 8000);\r\n    return () => clearInterval(bgInterval);\r\n  }, [backgrounds.length]);\r\n\r\n  return (\r\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\r\n      {/* Background with Parallax Effect */}\r\n      <div className=\"absolute inset-0\">\r\n        {backgrounds.map((bg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`absolute inset-0 transition-all duration-2000 ${\r\n              index === currentBg\r\n                ? \"opacity-100 scale-100\"\r\n                : \"opacity-0 scale-105\"\r\n            }`}\r\n          >\r\n            <Image\r\n              src={bg}\r\n              alt=\"HVAC Background\"\r\n              fill\r\n              className=\"object-cover\"\r\n              priority={index === 0}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {/* Modern Overlay */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900/90 via-blue-900/80 to-slate-900/90\" />\r\n\r\n        {/* Geometric Pattern Overlay */}\r\n        <div className=\"absolute inset-0 opacity-10\">\r\n          <div className=\"absolute top-20 left-20 w-32 h-32 border border-white rotate-45\" />\r\n          <div className=\"absolute bottom-20 right-20 w-24 h-24 border border-cyan-400 rotate-12\" />\r\n          <div className=\"absolute top-1/2 left-10 w-16 h-16 border border-blue-400 -rotate-12\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`relative z-10 w-full max-w-6xl mx-auto px-6 transition-all duration-1000 ${\r\n          isLoaded ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-8\"\r\n        }`}\r\n      >\r\n        {/* Hero Grid Layout */}\r\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]\">\r\n          {/* Left Column - Content */}\r\n          <div className=\"text-white space-y-8\">\r\n            {/* Main Heading */}\r\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight\">\r\n              <span className=\"text-white\">ST HVAC</span>\r\n              <br />\r\n              <span className=\"text-transparent bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text\">\r\n                SALES & SERVICES\r\n              </span>\r\n            </h1>\r\n\r\n            {/* Typewriter */}\r\n            <div className=\"text-xl sm:text-2xl\">\r\n              <TypewriterEffectSmooth\r\n                words={words}\r\n                className=\"text-xl sm:text-2xl lg:text-3xl\"\r\n                cursorClassName=\"bg-cyan-400\"\r\n              />\r\n            </div>\r\n\r\n            {/* Description */}\r\n            <p className=\"text-lg text-gray-300 leading-relaxed max-w-lg\">\r\n              Expert heating, cooling, and air quality solutions. From emergency\r\n              repairs to complete system installations, we deliver reliable\r\n              comfort for your home and business.\r\n            </p>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"px-8 py-4 bg-[#18dbc8] hover:bg-[#18dbc8] text-white rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                Learn More\r\n              </Link>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column - Service Cards */}\r\n          <div className=\"grid grid-cols-2 gap-6\">\r\n            {/* Service Card 1 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">❄️</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">\r\n                Air Conditioning\r\n              </h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Installation, repair & maintenance\r\n              </p>\r\n            </div>\r\n\r\n            {/* Service Card 2 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">🔥</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">Heating Systems</h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Furnaces, heat pumps & boilers\r\n              </p>\r\n            </div>\r\n\r\n            {/* Service Card 3 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">🌬️</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">Air Quality</h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Filtration & ventilation systems\r\n              </p>\r\n            </div>\r\n\r\n            {/* Service Card 4 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">🔧</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">Maintenance</h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Preventive care & tune-ups\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Background Indicators */}\r\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 z-20\">\r\n        {backgrounds.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => setCurrentBg(index)}\r\n            className={`w-2 h-2 rounded-full transition-all duration-300 ${\r\n              index === currentBg\r\n                ? \"bg-cyan-400 w-8\"\r\n                : \"bg-white/40 hover:bg-white/60\"\r\n            }`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Hero;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,OAAiB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,6BAA6B;IAC7B,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAW,WAAW;QAAuB;QACrD;YAAE,MAAM;YAAW,WAAW;QAA0B;QACxD;YAAE,MAAM;YAAQ,WAAW;QAAuB;QAClD;YAAE,MAAM;YAAW,WAAW;QAA0B;KACzD;IAED,sBAAsB;IACtB,MAAM,cAAc;QAAC;QAAmB;QAAoB;KAAc;IAE1E,IAAA,kNAAS,EAAC;QACR,YAAY;QACZ,MAAM,aAAa,YAAY;YAC7B,aAAa,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,YAAY,MAAM;QACxD,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,YAAY,MAAM;KAAC;IAEvB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;oBACZ,YAAY,GAAG,CAAC,CAAC,IAAI,sBACpB,8OAAC;4BAEC,WAAW,CAAC,8CAA8C,EACxD,UAAU,YACN,0BACA,uBACJ;sCAEF,cAAA,8OAAC,wIAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,IAAI;gCACJ,WAAU;gCACV,UAAU,UAAU;;;;;;2BAZjB;;;;;kCAkBT,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBACC,WAAW,CAAC,yEAAyE,EACnF,WAAW,8BAA8B,2BACzC;0BAGF,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAa;;;;;;sDAC7B,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAA2E;;;;;;;;;;;;8CAM7F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oLAAsB;wCACrB,OAAO;wCACP,WAAU;wCACV,iBAAgB;;;;;;;;;;;8CAKpB,8OAAC;oCAAE,WAAU;8CAAiD;;;;;;8CAO9D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uKAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAG9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,GAAG,sBACnB,8OAAC;wBAEC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,YACN,oBACA,iCACJ;uBANG;;;;;;;;;;;;;;;;AAYjB;uCAEe", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Image from 'next/image'\nimport { motion } from 'framer-motion'\n\nconst Client = () => {\n  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)\n\n  // Duplicate the array to create seamless loop\n  const duplicatedImages = [...clientImages, ...clientImages]\n\n  // Professional client statistics\n  const clientStats = [\n    { number: \"29+\", label: \"Trusted Partners\" },\n    { number: \"500+\", label: \"Projects Completed\" },\n    { number: \"98%\", label: \"Client Retention Rate\" },\n    { number: \"8+\", label: \"Years of Experience\" }\n  ];\n\n  // Client testimonials\n  const testimonials = [\n    {\n      quote: \"Exceptional service and professional installation. Our energy costs have decreased by 25% since the new HVAC system was installed.\",\n      author: \"<PERSON>\",\n      company: \"Metro Office Complex\",\n      role: \"Facility Manager\"\n    },\n    {\n      quote: \"Reliable, efficient, and always available when we need them. Their maintenance program has kept our systems running smoothly.\",\n      author: \"<PERSON>\",\n      company: \"TechStart Industries\",\n      role: \"Operations Director\"\n    }\n  ];\n\n  return (\n    <section className=\"py-16 lg:py-24 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Professional Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"flex items-center justify-center space-x-2 mb-6\">\n            <div className=\"w-12 h-px bg-blue-600\"></div>\n            <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">Our Clients</span>\n            <div className=\"w-12 h-px bg-blue-600\"></div>\n          </div>\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight\">\n            Trusted by Industry Leaders\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            We are proud to serve a diverse range of clients, from residential homeowners to large commercial\n            and industrial facilities, delivering reliable HVAC solutions that exceed expectations.\n          </p>\n        </motion.div>\n\n        {/* Professional Client Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\"\n        >\n          {clientStats.map((stat, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, scale: 0.9 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"text-center p-6 bg-white rounded-lg shadow-md border border-gray-100\"\n            >\n              <div className=\"text-3xl font-bold text-blue-600 mb-1\">{stat.number}</div>\n              <div className=\"text-sm text-gray-600 font-medium\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Professional Client Testimonials */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 gap-8 mb-16\"\n        >\n          {testimonials.map((testimonial, index) => (\n            <div key={index} className=\"bg-white p-8 rounded-lg shadow-lg border border-gray-100\">\n              <div className=\"flex items-start mb-4\">\n                <svg className=\"w-8 h-8 text-blue-600 mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z\"/>\n                </svg>\n                <div>\n                  <p className=\"text-gray-700 leading-relaxed mb-4\">\"{testimonial.quote}\"</p>\n                  <div>\n                    <p className=\"font-semibold text-gray-900\">{testimonial.author}</p>\n                    <p className=\"text-sm text-gray-600\">{testimonial.role}</p>\n                    <p className=\"text-sm text-blue-600 font-medium\">{testimonial.company}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </motion.div>\n\n        {/* Professional Client Logos Carousel */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"relative overflow-hidden rounded-lg bg-white border border-gray-100 shadow-md p-8\"\n        >\n          <div className=\"text-center mb-8\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">Trusted by Leading Organizations</h3>\n            <p className=\"text-gray-600\">Some of our valued clients and partners</p>\n          </div>\n\n          <div className=\"flex animate-scroll\">\n            {duplicatedImages.map((image, index) => (\n              <div\n                key={index}\n                className=\"flex-shrink-0 w-32 h-20 mx-4 relative\"\n              >\n                <div className=\"relative w-full h-full bg-gray-50 rounded-lg p-3 border border-gray-100\">\n                  <Image\n                    src={`/clients/${image}`}\n                    alt={`Client ${(index % clientImages.length) + 1}`}\n                    fill\n                    className=\"object-contain filter grayscale hover:grayscale-0 transition-all duration-300 p-2\"\n                    sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                  />\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* Gradient Overlays */}\n          <div className=\"absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-white to-transparent pointer-events-none\"></div>\n          <div className=\"absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-white to-transparent pointer-events-none\"></div>\n        </motion.div>\n\n        {/* Professional Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Ready to Experience Professional HVAC Service?\n          </h3>\n          <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n            Join our satisfied clients and experience the reliability, quality, and professionalism\n            that has made us a trusted HVAC partner.\n          </p>\n          <motion.button\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            className=\"inline-flex items-center gap-3 px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-300\"\n          >\n            <span>Contact Us Today</span>\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </motion.button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Client"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,SAAS;IACb,MAAM,eAAe,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAE9E,8CAA8C;IAC9C,MAAM,mBAAmB;WAAI;WAAiB;KAAa;IAE3D,iCAAiC;IACjC,MAAM,cAAc;QAClB;YAAE,QAAQ;YAAO,OAAO;QAAmB;QAC3C;YAAE,QAAQ;YAAQ,OAAO;QAAqB;QAC9C;YAAE,QAAQ;YAAO,OAAO;QAAwB;QAChD;YAAE,QAAQ;YAAM,OAAO;QAAsB;KAC9C;IAED,sBAAsB;IACtB,MAAM,eAAe;QACnB;YACE,OAAO;YACP,QAAQ;YACR,SAAS;YACT,MAAM;QACR;QACA;YACE,OAAO;YACP,QAAQ;YACR,SAAS;YACT,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA6D;;;;;;8CAC7E,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,oMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO,MAAM,QAAQ;4BAAI;4BACtD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CAAyC,KAAK,MAAM;;;;;;8CACnE,8OAAC;oCAAI,WAAU;8CAAqC,KAAK,KAAK;;;;;;;2BARzD;;;;;;;;;;8BAcX,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;4BAAgB,WAAU;sCACzB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA2C,MAAK;wCAAe,SAAQ;kDACpF,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;kDAEV,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;;oDAAqC;oDAAE,YAAY,KAAK;oDAAC;;;;;;;0DACtE,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAA+B,YAAY,MAAM;;;;;;kEAC9D,8OAAC;wDAAE,WAAU;kEAAyB,YAAY,IAAI;;;;;;kEACtD,8OAAC;wDAAE,WAAU;kEAAqC,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;2BAVnE;;;;;;;;;;8BAmBd,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;oCAEC,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wIAAK;4CACJ,KAAK,CAAC,SAAS,EAAE,OAAO;4CACxB,KAAK,CAAC,OAAO,EAAE,AAAC,QAAQ,aAAa,MAAM,GAAI,GAAG;4CAClD,IAAI;4CACJ,WAAU;4CACV,OAAM;;;;;;;;;;;mCATL;;;;;;;;;;sCAiBX,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA+C;;;;;;sCAI5D,8OAAC,oMAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;;8CAEV,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnF;uCAEe", "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo, useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\ntype GalleryImage = {\r\n  src: string;\r\n  alt: string;\r\n  category: string;\r\n  description: string;\r\n  location?: string;\r\n  year?: string;\r\n};\r\n\r\n// Professional project gallery\r\nconst projectImages: GalleryImage[] = [\r\n  {\r\n    src: \"/Picture2.jpg\",\r\n    alt: \"Commercial HVAC Installation\",\r\n    category: \"Commercial\",\r\n    description: \"Complete HVAC system installation for a 50,000 sq ft office building including energy-efficient units and smart controls.\",\r\n    location: \"Downtown Office Complex\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture4.jpg\",\r\n    alt: \"Industrial Ductwork System\",\r\n    category: \"Industrial\",\r\n    description: \"Custom ductwork design and installation for manufacturing facility with specialized ventilation requirements.\",\r\n    location: \"Manufacturing Plant\",\r\n    year: \"2023\"\r\n  },\r\n  {\r\n    src: \"/Picture5.jpg\",\r\n    alt: \"Smart Thermostat Installation\",\r\n    category: \"Residential\",\r\n    description: \"Smart thermostat and control system installation with mobile app integration and energy monitoring.\",\r\n    location: \"Residential Home\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture6.jpg\",\r\n    alt: \"Residential HVAC Upgrade\",\r\n    category: \"Residential\",\r\n    description: \"Complete residential HVAC system replacement with high-efficiency equipment and improved air quality.\",\r\n    location: \"Family Residence\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture8.jpg\",\r\n    alt: \"Preventive Maintenance Service\",\r\n    category: \"Maintenance\",\r\n    description: \"Comprehensive maintenance service including system inspection, cleaning, and performance optimization.\",\r\n    location: \"Various Locations\",\r\n    year: \"2024\"\r\n  },\r\n  {\r\n    src: \"/Picture11.jpg\",\r\n    alt: \"Energy Efficiency Retrofit\",\r\n    category: \"Commercial\",\r\n    description: \"Energy efficiency upgrade project resulting in 30% reduction in energy costs for retail facility.\",\r\n    location: \"Retail Center\",\r\n    year: \"2023\"\r\n  },\r\n];\r\n\r\nexport default function Gallery() {\r\n  const images = useMemo(() => projectImages, []);\r\n  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);\r\n  const [filter, setFilter] = useState<string>(\"All\");\r\n\r\n  const categories = [\"All\", ...Array.from(new Set(images.map((img: GalleryImage) => img.category)))];\r\n  const filteredImages = filter === \"All\" ? images : images.filter((img: GalleryImage) => img.category === filter);\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-white\">\r\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        {/* Professional Header */}\r\n        <motion.header\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <div className=\"flex items-center justify-center space-x-2 mb-6\">\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n            <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">Project Gallery</span>\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight\">\r\n            Our Recent HVAC Projects\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Explore our portfolio of successful HVAC installations, maintenance projects, and energy efficiency upgrades\r\n            across residential, commercial, and industrial properties.\r\n          </p>\r\n        </motion.header>\r\n\r\n        {/* Professional Category Filter */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.2 }}\r\n          viewport={{ once: true }}\r\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\r\n        >\r\n          {categories.map((category: string) => (\r\n            <button\r\n              key={category}\r\n              onClick={() => setFilter(category)}\r\n              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${\r\n                filter === category\r\n                  ? \"bg-blue-600 text-white shadow-lg\"\r\n                  : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"\r\n              }`}\r\n            >\r\n              {category}\r\n            </button>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Professional Gallery Grid */}\r\n        <motion.div\r\n          layout\r\n          className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\"\r\n        >\r\n          <AnimatePresence>\r\n            {filteredImages.map((image, index) => (\r\n              <motion.div\r\n                key={image.src}\r\n                layout\r\n                initial={{ opacity: 0, scale: 0.9 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                exit={{ opacity: 0, scale: 0.9 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                className=\"group relative cursor-pointer\"\r\n                onClick={() => setSelectedImage(image)}\r\n              >\r\n                <div className=\"relative overflow-hidden rounded-lg bg-white shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100\">\r\n                  <div className=\"relative aspect-[4/3] overflow-hidden\">\r\n                    <img\r\n                      src={image.src}\r\n                      alt={image.alt}\r\n                      loading=\"lazy\"\r\n                      className=\"h-full w-full object-cover transition duration-300 group-hover:scale-105\"\r\n                    />\r\n\r\n                    {/* Overlay */}\r\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\r\n\r\n                    {/* Category Badge */}\r\n                    <div className=\"absolute top-4 left-4 px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded\">\r\n                      {image.category}\r\n                    </div>\r\n\r\n                    {/* Project Info */}\r\n                    {image.year && (\r\n                      <div className=\"absolute top-4 right-4 px-3 py-1 bg-white/90 text-gray-800 text-sm font-medium rounded\">\r\n                        {image.year}\r\n                      </div>\r\n                    )}\r\n\r\n                    {/* Content */}\r\n                    <div className=\"absolute inset-x-0 bottom-0 p-6 text-white transform translate-y-2 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300\">\r\n                      <h3 className=\"text-lg font-bold mb-1\">{image.alt}</h3>\r\n                      {image.location && (\r\n                        <p className=\"text-sm text-white/80 mb-2\">{image.location}</p>\r\n                      )}\r\n                      <p className=\"text-sm text-white/90 leading-relaxed line-clamp-2\">{image.description}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </AnimatePresence>\r\n        </motion.div>\r\n\r\n        {/* Professional Lightbox Modal */}\r\n        <AnimatePresence>\r\n          {selectedImage && (\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/80 p-4\"\r\n              onClick={() => setSelectedImage(null)}\r\n            >\r\n              <motion.div\r\n                initial={{ scale: 0.9, opacity: 0 }}\r\n                animate={{ scale: 1, opacity: 1 }}\r\n                exit={{ scale: 0.9, opacity: 0 }}\r\n                className=\"relative max-w-4xl max-h-[90vh] bg-white rounded-lg overflow-hidden shadow-2xl\"\r\n                onClick={(e) => e.stopPropagation()}\r\n              >\r\n                <div className=\"relative\">\r\n                  <img\r\n                    src={selectedImage.src}\r\n                    alt={selectedImage.alt}\r\n                    className=\"w-full h-auto max-h-[60vh] object-cover\"\r\n                  />\r\n\r\n                  {/* Close Button */}\r\n                  <button\r\n                    onClick={() => setSelectedImage(null)}\r\n                    className=\"absolute top-4 right-4 w-10 h-10 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300\"\r\n                  >\r\n                    <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                    </svg>\r\n                  </button>\r\n\r\n                  {/* Category Badge */}\r\n                  <div className=\"absolute top-4 left-4 px-3 py-1 bg-blue-600 text-white text-sm font-medium rounded\">\r\n                    {selectedImage.category}\r\n                  </div>\r\n\r\n                  {/* Year Badge */}\r\n                  {selectedImage.year && (\r\n                    <div className=\"absolute top-4 left-20 px-3 py-1 bg-white/90 text-gray-800 text-sm font-medium rounded\">\r\n                      {selectedImage.year}\r\n                    </div>\r\n                  )}\r\n                </div>\r\n\r\n                {/* Professional Content */}\r\n                <div className=\"p-6\">\r\n                  <div className=\"flex items-start justify-between mb-4\">\r\n                    <div>\r\n                      <h3 className=\"text-2xl font-bold text-gray-900 mb-1\">{selectedImage.alt}</h3>\r\n                      {selectedImage.location && (\r\n                        <p className=\"text-blue-600 font-medium\">{selectedImage.location}</p>\r\n                      )}\r\n                    </div>\r\n                    {selectedImage.year && (\r\n                      <span className=\"text-gray-500 text-sm\">{selectedImage.year}</span>\r\n                    )}\r\n                  </div>\r\n                  <p className=\"text-gray-600 leading-relaxed\">{selectedImage.description}</p>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAHA;;;;AAcA,+BAA+B;AAC/B,MAAM,gBAAgC;IACpC;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;QACV,MAAM;IACR;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,IAAA,gNAAO,EAAC,IAAM,eAAe,EAAE;IAC9C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAsB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAS;IAE7C,MAAM,aAAa;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,MAAsB,IAAI,QAAQ;KAAI;IACnG,MAAM,iBAAiB,WAAW,QAAQ,SAAS,OAAO,MAAM,CAAC,CAAC,MAAsB,IAAI,QAAQ,KAAK;IAEzG,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,oMAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA6D;;;;;;8CAC7E,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4BAEC,SAAS,IAAM,UAAU;4BACzB,WAAW,CAAC,6DAA6D,EACvE,WAAW,WACP,qCACA,+CACJ;sCAED;2BARI;;;;;;;;;;8BAcX,8OAAC,oMAAM,CAAC,GAAG;oBACT,MAAM;oBACN,WAAU;8BAEV,cAAA,8OAAC,4MAAe;kCACb,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,oMAAM,CAAC,GAAG;gCAET,MAAM;gCACN,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,MAAM;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAC/B,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,WAAU;gCACV,SAAS,IAAM,iBAAiB;0CAEhC,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,MAAM,GAAG;gDACd,KAAK,MAAM,GAAG;gDACd,SAAQ;gDACR,WAAU;;;;;;0DAIZ,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;0DACZ,MAAM,QAAQ;;;;;;4CAIhB,MAAM,IAAI,kBACT,8OAAC;gDAAI,WAAU;0DACZ,MAAM,IAAI;;;;;;0DAKf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA0B,MAAM,GAAG;;;;;;oDAChD,MAAM,QAAQ,kBACb,8OAAC;wDAAE,WAAU;kEAA8B,MAAM,QAAQ;;;;;;kEAE3D,8OAAC;wDAAE,WAAU;kEAAsD,MAAM,WAAW;;;;;;;;;;;;;;;;;;;;;;;+BAvCrF,MAAM,GAAG;;;;;;;;;;;;;;;8BAiDtB,8OAAC,4MAAe;8BACb,+BACC,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,MAAM;4BAAE,SAAS;wBAAE;wBACnB,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCAEhC,cAAA,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAClC,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,MAAM;gCAAE,OAAO;gCAAK,SAAS;4BAAE;4BAC/B,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8CAEjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK,cAAc,GAAG;4CACtB,KAAK,cAAc,GAAG;4CACtB,WAAU;;;;;;sDAIZ,8OAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAKzE,8OAAC;4CAAI,WAAU;sDACZ,cAAc,QAAQ;;;;;;wCAIxB,cAAc,IAAI,kBACjB,8OAAC;4CAAI,WAAU;sDACZ,cAAc,IAAI;;;;;;;;;;;;8CAMzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyC,cAAc,GAAG;;;;;;wDACvE,cAAc,QAAQ,kBACrB,8OAAC;4DAAE,WAAU;sEAA6B,cAAc,QAAQ;;;;;;;;;;;;gDAGnE,cAAc,IAAI,kBACjB,8OAAC;oDAAK,WAAU;8DAAyB,cAAc,IAAI;;;;;;;;;;;;sDAG/D,8OAAC;4CAAE,WAAU;sDAAiC,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzF", "debugId": null}}, {"offset": {"line": 1437, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link'\r\nimport Image from 'next/image'\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst AboutTeaser = () => {\r\n  const certifications = [\r\n    {\r\n      name: \"EPA Certified\",\r\n      description: \"Environmental Protection Agency certified technicians\"\r\n    },\r\n    {\r\n      name: \"NATE Certified\",\r\n      description: \"North American Technician Excellence certification\"\r\n    },\r\n    {\r\n      name: \"Licensed & Insured\",\r\n      description: \"Fully licensed and insured for your protection\"\r\n    }\r\n  ];\r\n\r\n  const keyFeatures = [\r\n    {\r\n      title: \"Licensed Professionals\",\r\n      description: \"Our team consists of fully licensed HVAC technicians with extensive industry experience and ongoing training.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      title: \"24/7 Emergency Service\",\r\n      description: \"Round-the-clock emergency HVAC services to ensure your comfort is never compromised.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      title: \"Energy Efficient Solutions\",\r\n      description: \"Modern HVAC systems designed to reduce energy costs while maintaining optimal indoor comfort.\",\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n        </svg>\r\n      )\r\n    }\r\n  ];\r\n\r\n  const companyStats = [\r\n    { number: \"8+\", label: \"Years Experience\" },\r\n    { number: \"150+\", label: \"Projects Completed\" },\r\n    { number: \"100%\", label: \"Client Satisfaction\" },\r\n    { number: \"24/7\", label: \"Emergency Service\" }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-white\">\r\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\r\n\r\n          {/* Left Content - Professional Images */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -30 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.6 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            {/* Main Professional Image */}\r\n            <div className=\"relative\">\r\n              <div className=\"relative h-[500px] lg:h-[600px] rounded-lg overflow-hidden shadow-lg\">\r\n                <Image\r\n                  src=\"/about1.jpg\"\r\n                  alt=\"Professional HVAC Technician at Work\"\r\n                  fill\r\n                  className=\"object-cover\"\r\n                  sizes=\"(max-width: 768px) 100vw, 50vw\"\r\n                  priority\r\n                />\r\n              </div>\r\n\r\n              {/* Professional Stats Card */}\r\n              <div className=\"absolute -bottom-6 -right-6 bg-white rounded-lg p-6 shadow-xl border\">\r\n                <div className=\"grid grid-cols-2 gap-4 text-center\">\r\n                  <div>\r\n                    <div className=\"text-2xl font-bold text-blue-600\">8+</div>\r\n                    <div className=\"text-sm text-gray-600\">Years</div>\r\n                  </div>\r\n                  <div>\r\n                    <div className=\"text-2xl font-bold text-blue-600\">150+</div>\r\n                    <div className=\"text-sm text-gray-600\">Projects</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Secondary Professional Image */}\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.9 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"absolute top-16 -right-6 w-1/2 h-[280px] lg:h-[320px] rounded-lg overflow-hidden shadow-lg\"\r\n            >\r\n              <Image\r\n                src=\"/about2.jpg\"\r\n                alt=\"HVAC System Installation\"\r\n                fill\r\n                className=\"object-cover\"\r\n                sizes=\"(max-width: 768px) 50vw, 25vw\"\r\n              />\r\n\r\n              {/* Professional Badge */}\r\n              <div className=\"absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium\">\r\n                Licensed & Insured\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Certifications */}\r\n            <div className=\"absolute -bottom-4 left-0 bg-white rounded-lg p-4 shadow-lg border\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\r\n                  <svg className=\"w-4 h-4 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n                    <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </div>\r\n                <div>\r\n                  <div className=\"text-sm font-medium text-gray-900\">EPA Certified</div>\r\n                  <div className=\"text-xs text-gray-500\">Licensed Professionals</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Right Content */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-8\"\r\n          >\r\n            {/* Professional Section Label */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"flex items-center space-x-2\"\r\n            >\r\n              <div className=\"w-12 h-px bg-blue-600\"></div>\r\n              <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">About Our Company</span>\r\n            </motion.div>\r\n\r\n            {/* Professional Heading */}\r\n            <motion.h2\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 leading-tight\"\r\n            >\r\n              Professional HVAC Services You Can Trust\r\n            </motion.h2>\r\n\r\n            {/* Professional Description */}\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-lg text-gray-600 leading-relaxed\"\r\n            >\r\n              With over 8 years of experience in the HVAC industry, we provide comprehensive heating,\r\n              ventilation, and air conditioning services for residential and commercial properties.\r\n              Our licensed technicians are committed to delivering quality workmanship and exceptional customer service.\r\n            </motion.p>\r\n\r\n            {/* Company Stats */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"grid grid-cols-2 gap-6\"\r\n            >\r\n              {companyStats.slice(2).map((stat, index) => (\r\n                <div key={index} className=\"text-center p-4 bg-gray-50 rounded-lg\">\r\n                  <div className=\"text-3xl font-bold text-blue-600 mb-1\">{stat.number}</div>\r\n                  <div className=\"text-sm text-gray-600\">{stat.label}</div>\r\n                </div>\r\n              ))}\r\n            </motion.div>\r\n\r\n            {/* Professional Key Features */}\r\n            <div className=\"space-y-4\">\r\n              {keyFeatures.map((feature, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"flex items-start gap-4 p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\r\n                >\r\n                  <div className=\"flex-shrink-0 w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                    {feature.icon}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"text-lg font-semibold text-gray-900 mb-1\">\r\n                      {feature.title}\r\n                    </h4>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">\r\n                      {feature.description}\r\n                    </p>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Certifications */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.5 }}\r\n              viewport={{ once: true }}\r\n              className=\"flex flex-wrap gap-4\"\r\n            >\r\n              {certifications.map((cert, index) => (\r\n                <div key={index} className=\"flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-lg\">\r\n                  <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\r\n                  <span className=\"text-sm font-medium text-blue-800\">{cert.name}</span>\r\n                </div>\r\n              ))}\r\n            </motion.div>\r\n\r\n            {/* Professional CTA Button */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"pt-4\"\r\n            >\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"inline-flex items-center gap-2 px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors duration-300\"\r\n              >\r\n                Learn More About Us\r\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\r\n                </svg>\r\n              </Link>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default AboutTeaser\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,cAAc;IAClB,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,aAAa;QACf;KACD;IAED,MAAM,cAAc;QAClB;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KACD;IAED,MAAM,eAAe;QACnB;YAAE,QAAQ;YAAM,OAAO;QAAmB;QAC1C;YAAE,QAAQ;YAAQ,OAAO;QAAqB;QAC9C;YAAE,QAAQ;YAAQ,OAAO;QAAsB;QAC/C;YAAE,QAAQ;YAAQ,OAAO;QAAoB;KAC9C;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wIAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,OAAM;4CACN,QAAQ;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC,wIAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,OAAM;;;;;;kDAIR,8OAAC;wCAAI,WAAU;kDAAqF;;;;;;;;;;;;0CAMtG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAkiB,UAAS;;;;;;;;;;;;;;;;sDAG1kB,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAA6D;;;;;;;;;;;;0CAI/E,8OAAC,oMAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;0CAKD,8OAAC,oMAAM,CAAC,CAAC;gCACP,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CACX;;;;;;0CAOD,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAET,aAAa,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,sBAChC,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DAAyC,KAAK,MAAM;;;;;;0DACnE,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,KAAK;;;;;;;uCAF1C;;;;;;;;;;0CAQd,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,8OAAC,oMAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,MAAM,QAAQ;wCAAI;wCACtD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;uCAfnB;;;;;;;;;;0CAuBX,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAET,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAqC,KAAK,IAAI;;;;;;;uCAFtD;;;;;;;;;;0CAQd,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;0CAEV,cAAA,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;;wCACX;sDAEC,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvF;uCAEe", "debugId": null}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\n\r\nexport function WhyChooseUsSection() {\r\n  const reasons = [\r\n    {\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      ),\r\n      title: \"24/7 Emergency Service\",\r\n      description: \"Round-the-clock emergency HVAC services with guaranteed response times. Our certified technicians are available when you need them most.\",\r\n      highlight: \"2-Hour Response Time\"\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n        </svg>\r\n      ),\r\n      title: \"Licensed & Insured\",\r\n      description: \"Fully licensed HVAC contractors with comprehensive insurance coverage. All work performed meets or exceeds industry standards and local codes.\",\r\n      highlight: \"EPA Certified\"\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n        </svg>\r\n      ),\r\n      title: \"Energy Efficient Solutions\",\r\n      description: \"Modern HVAC systems designed to reduce energy costs by up to 30%. Smart thermostats and high-efficiency equipment for optimal performance.\",\r\n      highlight: \"Up to 30% Savings\"\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\r\n        </svg>\r\n      ),\r\n      title: \"Customer Satisfaction\",\r\n      description: \"Committed to exceeding customer expectations with quality workmanship and reliable service. Comprehensive warranties on all installations.\",\r\n      highlight: \"100% Satisfaction Guarantee\"\r\n    }\r\n  ];\r\n\r\n  const services = [\r\n    {\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\r\n        </svg>\r\n      ),\r\n      title: \"Commercial HVAC\",\r\n      description: \"Complete commercial heating and cooling solutions\"\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\r\n        </svg>\r\n      ),\r\n      title: \"Residential HVAC\",\r\n      description: \"Home comfort solutions and energy efficiency\"\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n        </svg>\r\n      ),\r\n      title: \"Maintenance Plans\",\r\n      description: \"Preventive maintenance and service contracts\"\r\n    },\r\n    {\r\n      icon: (\r\n        <svg className=\"w-6 h-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\r\n        </svg>\r\n      ),\r\n      title: \"Energy Audits\",\r\n      description: \"Efficiency assessments and optimization\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"py-16 lg:py-24 bg-gray-50\">\r\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\r\n        {/* Professional Section Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <div className=\"flex items-center justify-center space-x-2 mb-6\">\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n            <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">Why Choose Us</span>\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n          </div>\r\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight\">\r\n            Professional HVAC Services You Can Trust\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            With years of experience and a commitment to excellence, we provide reliable HVAC solutions\r\n            for residential and commercial properties throughout the region.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Main Reasons Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-20\">\r\n          {reasons.map((reason, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100\"\r\n            >\r\n              <div className=\"flex items-start gap-6\">\r\n                <div className=\"flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\r\n                  {reason.icon}\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">\r\n                    {reason.title}\r\n                  </h3>\r\n                  <div className=\"inline-flex items-center gap-2 px-3 py-1 bg-blue-50 text-blue-700 text-sm font-medium rounded-full mb-4\">\r\n                    <span className=\"w-2 h-2 bg-blue-600 rounded-full\"></span>\r\n                    {reason.highlight}\r\n                  </div>\r\n                  <p className=\"text-gray-600 leading-relaxed\">\r\n                    {reason.description}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Specialties Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <div className=\"flex items-center justify-center space-x-2 mb-6\">\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n            <span className=\"text-blue-600 font-medium text-sm uppercase tracking-wider\">Our Services</span>\r\n            <div className=\"w-12 h-px bg-blue-600\"></div>\r\n          </div>\r\n          <h3 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n            Comprehensive HVAC Solutions\r\n          </h3>\r\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed\">\r\n            From installation to maintenance, we provide complete HVAC services\r\n            for residential and commercial properties.\r\n          </p>\r\n        </motion.div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n          {services.map((service, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, scale: 0.9 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.5, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-100\"\r\n            >\r\n              <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\">\r\n                {service.icon}\r\n              </div>\r\n              <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">\r\n                {service.title}\r\n              </h4>\r\n              <p className=\"text-gray-600 text-sm\">\r\n                {service.description}\r\n              </p>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKO,SAAS;IACd,MAAM,UAAU;QACd;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,WAAW;QACb;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,WAAW;QACf;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;;kCAC/E,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;QACA;YACE,oBACE,8OAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA6D;;;;;;8CAC7E,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAOzE,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,oMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,OAAO,IAAI;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,OAAO,KAAK;;;;;;0DAEf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;;;;;oDACf,OAAO,SAAS;;;;;;;0DAEnB,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;2BApBpB;;;;;;;;;;8BA6BX,8OAAC,oMAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA6D;;;;;;8CAC7E,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,8OAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAMzE,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,oMAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAEf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAdjB;;;;;;;;;;;;;;;;;;;;;AAsBnB", "debugId": null}}]}