{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/lib/utils.ts"], "sourcesContent": ["import { ClassValue, clsx } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/ui/typewriter-effect-smooth.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport const TypewriterEffectSmooth = ({\r\n  words,\r\n  className,\r\n  cursorClassName,\r\n}: {\r\n  words: {\r\n    text: string;\r\n    className?: string;\r\n  }[];\r\n  className?: string;\r\n  cursorClassName?: string;\r\n}) => {\r\n  const [displayedText, setDisplayedText] = useState(\"\");\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n\r\n  // Combine all words into one continuous text\r\n  const fullText = words.map(word => word.text).join(\" \");\r\n  const firstWordClass = words[0]?.className || \"\";\r\n\r\n  useEffect(() => {\r\n    const timeout = setTimeout(() => {\r\n      if (currentIndex < fullText.length) {\r\n        // Typing the full sentence\r\n        setDisplayedText(fullText.substring(0, currentIndex + 1));\r\n        setCurrentIndex(currentIndex + 1);\r\n      }\r\n      // Once fully typed, keep it displayed (no deleting or cycling)\r\n    }, 100); // Typing speed\r\n\r\n    return () => clearTimeout(timeout);\r\n  }, [currentIndex, fullText]);\r\n\r\n  return (\r\n    <div className={cn(\"flex items-center gap-1\", className)}>\r\n      <div className=\"text-xs sm:text-base md:text-xl lg:text-3xl xl:text-5xl font-bold\">\r\n        <span className={cn(\"text-blue-800 font-medium italic\", firstWordClass)}>\r\n          {displayedText}\r\n        </span>\r\n        <span \r\n          className={cn(\r\n            \"inline-block w-0.5 h-4 sm:h-6 xl:h-12 bg-blue-800 ml-1 animate-pulse\",\r\n            cursorClassName\r\n          )}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKO,MAAM,yBAAyB,CAAC,EACrC,KAAK,EACL,SAAS,EACT,eAAe,EAQhB;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAC;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAAC;IAEjD,6CAA6C;IAC7C,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,IAAI,CAAC;IACnD,MAAM,iBAAiB,KAAK,CAAC,EAAE,EAAE,aAAa;IAE9C,IAAA,kNAAS,EAAC;QACR,MAAM,UAAU,WAAW;YACzB,IAAI,eAAe,SAAS,MAAM,EAAE;gBAClC,2BAA2B;gBAC3B,iBAAiB,SAAS,SAAS,CAAC,GAAG,eAAe;gBACtD,gBAAgB,eAAe;YACjC;QACA,+DAA+D;QACjE,GAAG,MAAM,eAAe;QAExB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;KAAS;IAE3B,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,2BAA2B;kBAC5C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAW,IAAA,yHAAE,EAAC,oCAAoC;8BACrD;;;;;;8BAEH,8OAAC;oBACC,WAAW,IAAA,yHAAE,EACX,wEACA;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { TypewriterEffectSmooth } from \"@/components/ui/typewriter-effect-smooth\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nconst Hero: React.FC = () => {\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n  const [currentBg, setCurrentBg] = useState(0);\r\n\r\n  // Typewriter animation words\r\n  const words = [\r\n    { text: \"Breathe\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-blue-400 font-bold\" },\r\n    { text: \"Live\", className: \"text-white font-bold\" },\r\n    { text: \"Easy...\", className: \"text-cyan-400 font-bold\" },\r\n  ];\r\n\r\n  // Background rotation\r\n  const backgrounds = [\"/background.jpg\", \"/background1.jpg\", \"/about1.jpg\"];\r\n\r\n  useEffect(() => {\r\n    setIsLoaded(true);\r\n    const bgInterval = setInterval(() => {\r\n      setCurrentBg((prev) => (prev + 1) % backgrounds.length);\r\n    }, 8000);\r\n    return () => clearInterval(bgInterval);\r\n  }, [backgrounds.length]);\r\n\r\n  return (\r\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\r\n      {/* Background with Parallax Effect */}\r\n      <div className=\"absolute inset-0\">\r\n        {backgrounds.map((bg, index) => (\r\n          <div\r\n            key={index}\r\n            className={`absolute inset-0 transition-all duration-2000 ${\r\n              index === currentBg\r\n                ? \"opacity-100 scale-100\"\r\n                : \"opacity-0 scale-105\"\r\n            }`}\r\n          >\r\n            <Image\r\n              src={bg}\r\n              alt=\"HVAC Background\"\r\n              fill\r\n              className=\"object-cover\"\r\n              priority={index === 0}\r\n            />\r\n          </div>\r\n        ))}\r\n\r\n        {/* Modern Overlay */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-br from-slate-900/90 via-blue-900/80 to-slate-900/90\" />\r\n\r\n        {/* Geometric Pattern Overlay */}\r\n        <div className=\"absolute inset-0 opacity-10\">\r\n          <div className=\"absolute top-20 left-20 w-32 h-32 border border-white rotate-45\" />\r\n          <div className=\"absolute bottom-20 right-20 w-24 h-24 border border-cyan-400 rotate-12\" />\r\n          <div className=\"absolute top-1/2 left-10 w-16 h-16 border border-blue-400 -rotate-12\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Main Content */}\r\n      <div\r\n        className={`relative z-10 w-full max-w-6xl mx-auto px-6 transition-all duration-1000 ${\r\n          isLoaded ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-8\"\r\n        }`}\r\n      >\r\n        {/* Hero Grid Layout */}\r\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]\">\r\n          {/* Left Column - Content */}\r\n          <div className=\"text-white space-y-8\">\r\n            {/* Main Heading */}\r\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight\">\r\n              <span className=\"text-white\">ST HVAC</span>\r\n              <br />\r\n              <span className=\"text-transparent bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text\">\r\n                SALES & SERVICES\r\n              </span>\r\n            </h1>\r\n\r\n            {/* Typewriter */}\r\n            <div className=\"text-xl sm:text-2xl\">\r\n              <TypewriterEffectSmooth\r\n                words={words}\r\n                className=\"text-xl sm:text-2xl lg:text-3xl\"\r\n                cursorClassName=\"bg-cyan-400\"\r\n              />\r\n            </div>\r\n\r\n            {/* Description */}\r\n            <p className=\"text-lg text-gray-300 leading-relaxed max-w-lg\">\r\n              Expert heating, cooling, and air quality solutions. From emergency\r\n              repairs to complete system installations, we deliver reliable\r\n              comfort for your home and business.\r\n            </p>\r\n\r\n            {/* Action Buttons */}\r\n            <div className=\"flex flex-col sm:flex-row gap-4\">\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"px-8 py-4 bg-[#18dbc8] hover:bg-[#18dbc8] text-white rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\r\n              >\r\n                Learn More\r\n              </Link>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Column - Service Cards */}\r\n          <div className=\"grid grid-cols-2 gap-6\">\r\n            {/* Service Card 1 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">❄️</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">\r\n                Air Conditioning\r\n              </h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Installation, repair & maintenance\r\n              </p>\r\n            </div>\r\n\r\n            {/* Service Card 2 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">🔥</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">Heating Systems</h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Furnaces, heat pumps & boilers\r\n              </p>\r\n            </div>\r\n\r\n            {/* Service Card 3 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">🌬️</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">Air Quality</h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Filtration & ventilation systems\r\n              </p>\r\n            </div>\r\n\r\n            {/* Service Card 4 */}\r\n            <div className=\"group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2\">\r\n              <div className=\"text-3xl mb-4\">🔧</div>\r\n              <h3 className=\"text-white font-semibold mb-2\">Maintenance</h3>\r\n              <p className=\"text-gray-300 text-sm\">\r\n                Preventive care & tune-ups\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Background Indicators */}\r\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 z-20\">\r\n        {backgrounds.map((_, index) => (\r\n          <button\r\n            key={index}\r\n            onClick={() => setCurrentBg(index)}\r\n            className={`w-2 h-2 rounded-full transition-all duration-300 ${\r\n              index === currentBg\r\n                ? \"bg-cyan-400 w-8\"\r\n                : \"bg-white/40 hover:bg-white/60\"\r\n            }`}\r\n          />\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Hero;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,OAAiB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,6BAA6B;IAC7B,MAAM,QAAQ;QACZ;YAAE,MAAM;YAAW,WAAW;QAAuB;QACrD;YAAE,MAAM;YAAW,WAAW;QAA0B;QACxD;YAAE,MAAM;YAAQ,WAAW;QAAuB;QAClD;YAAE,MAAM;YAAW,WAAW;QAA0B;KACzD;IAED,sBAAsB;IACtB,MAAM,cAAc;QAAC;QAAmB;QAAoB;KAAc;IAE1E,IAAA,kNAAS,EAAC;QACR,YAAY;QACZ,MAAM,aAAa,YAAY;YAC7B,aAAa,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,YAAY,MAAM;QACxD,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,YAAY,MAAM;KAAC;IAEvB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;oBACZ,YAAY,GAAG,CAAC,CAAC,IAAI,sBACpB,8OAAC;4BAEC,WAAW,CAAC,8CAA8C,EACxD,UAAU,YACN,0BACA,uBACJ;sCAEF,cAAA,8OAAC,wIAAK;gCACJ,KAAK;gCACL,KAAI;gCACJ,IAAI;gCACJ,WAAU;gCACV,UAAU,UAAU;;;;;;2BAZjB;;;;;kCAkBT,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBACC,WAAW,CAAC,yEAAyE,EACnF,WAAW,8BAA8B,2BACzC;0BAGF,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAa;;;;;;sDAC7B,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAA2E;;;;;;;;;;;;8CAM7F,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oLAAsB;wCACrB,OAAO;wCACP,WAAU;wCACV,iBAAgB;;;;;;;;;;;8CAKpB,8OAAC;oCAAE,WAAU;8CAAiD;;;;;;8CAO9D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,uKAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAG9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAMvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,GAAG,sBACnB,8OAAC;wBAEC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,YACN,oBACA,iCACJ;uBANG;;;;;;;;;;;;;;;;AAYjB;uCAEe", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport Image from 'next/image'\nimport { motion } from 'framer-motion'\n\nconst Client = () => {\n  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)\n\n  // Duplicate the array to create seamless loop\n  const duplicatedImages = [...clientImages, ...clientImages]\n\n  // Client testimonials/stats\n  const clientStats = [\n    { number: \"29+\", label: \"Trusted Partners\", icon: \"🤝\" },\n    { number: \"500+\", label: \"Projects Delivered\", icon: \"🏗️\" },\n    { number: \"98%\", label: \"Client Retention\", icon: \"⭐\" },\n    { number: \"24/7\", label: \"Support Available\", icon: \"🕒\" }\n  ];\n\n  return (\n    <section className=\"relative py-20 bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50 overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-400/20 to-teal-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Enhanced Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <div className=\"inline-flex items-center gap-3 px-8 py-4 rounded-full bg-gradient-to-r from-blue-100 via-cyan-100 to-emerald-100 text-blue-800 text-sm font-bold mb-8 shadow-lg\">\n            <span className=\"w-3 h-3 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full animate-pulse\"></span>\n            Our Valued Partners\n            <span className=\"w-3 h-3 bg-gradient-to-r from-cyan-600 to-emerald-600 rounded-full animate-pulse delay-500\"></span>\n          </div>\n          <h2 className=\"text-5xl md:text-6xl font-bold mb-6 leading-tight\">\n            <span className=\"text-gray-900\">Trusted by</span>{' '}\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-cyan-600 to-emerald-600\">\n              Industry Leaders\n            </span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            We are proud to work with industry leaders and trusted partners who rely on our\n            HVAC expertise, quality service, and innovative solutions.\n          </p>\n        </motion.div>\n\n        {/* Client Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-16\"\n        >\n          {clientStats.map((stat, index) => (\n            <motion.div\n              key={index}\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1\"\n            >\n              <div className=\"text-4xl mb-3\">{stat.icon}</div>\n              <div className=\"text-3xl font-bold text-gray-900 mb-1\">{stat.number}</div>\n              <div className=\"text-sm text-gray-600 font-medium\">{stat.label}</div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Enhanced Carousel Container */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"relative\"\n        >\n          {/* Enhanced Gradient overlays */}\n          <div className=\"absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-slate-50 via-slate-50/80 to-transparent z-10\"></div>\n          <div className=\"absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-slate-50 via-slate-50/80 to-transparent z-10\"></div>\n\n          {/* Enhanced Moving Carousel */}\n          <div className=\"flex animate-scroll\">\n            {duplicatedImages.map((image, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: (index % 29) * 0.05 }}\n                className=\"group flex-shrink-0 mx-6 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 flex items-center justify-center w-56 h-40 border border-white/30 hover:border-blue-200 hover:-translate-y-2\"\n              >\n                <Image\n                  src={`/${image}`}\n                  alt={`Client ${(index % 29) + 1}`}\n                  width={140}\n                  height={100}\n                  className=\"object-contain max-w-full max-h-full group-hover:scale-110 transition-all duration-500\"\n                  style={{ filter: 'grayscale(100%) brightness(0.8)' }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.filter = 'grayscale(0%) brightness(1)'\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.filter = 'grayscale(100%) brightness(0.8)'\n                  }}\n                />\n\n                {/* Hover Effect Overlay */}\n                <div className=\"absolute inset-0 bg-gradient-to-t from-blue-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl\"></div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Enhanced Additional Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"inline-flex items-center gap-4 px-8 py-4 bg-white/80 backdrop-blur-sm rounded-full border border-white/30 shadow-lg\">\n            <div className=\"flex items-center gap-2\">\n              <span className=\"w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full animate-pulse\"></span>\n              <span className=\"text-gray-700 font-semibold\">Trusted by 29+ companies and growing</span>\n            </div>\n            <div className=\"w-px h-6 bg-gray-300\"></div>\n            <div className=\"flex items-center gap-2\">\n              <span className=\"w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse delay-500\"></span>\n              <span className=\"text-gray-700 font-semibold\">98% Client Retention Rate</span>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Client"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,SAAS;IACb,MAAM,eAAe,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IAE9E,8CAA8C;IAC9C,MAAM,mBAAmB;WAAI;WAAiB;KAAa;IAE3D,4BAA4B;IAC5B,MAAM,cAAc;QAClB;YAAE,QAAQ;YAAO,OAAO;YAAoB,MAAM;QAAK;QACvD;YAAE,QAAQ;YAAQ,OAAO;YAAsB,MAAM;QAAM;QAC3D;YAAE,QAAQ;YAAO,OAAO;YAAoB,MAAM;QAAI;QACtD;YAAE,QAAQ;YAAQ,OAAO;YAAqB,MAAM;QAAK;KAC1D;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;oCAAuF;kDAEvG,8OAAC;wCAAK,WAAU;;;;;;;;;;;;0CAElB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAkB;kDAClD,8OAAC;wCAAK,WAAU;kDAA2F;;;;;;;;;;;;0CAI7G,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,oMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;gCACtD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAiB,KAAK,IAAI;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAyC,KAAK,MAAM;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAqC,KAAK,KAAK;;;;;;;+BATzD;;;;;;;;;;kCAeX,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC,oMAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,AAAC,QAAQ,KAAM;wCAAK;wCACxD,WAAU;;0DAEV,8OAAC,wIAAK;gDACJ,KAAK,CAAC,CAAC,EAAE,OAAO;gDAChB,KAAK,CAAC,OAAO,EAAE,AAAC,QAAQ,KAAM,GAAG;gDACjC,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,OAAO;oDAAE,QAAQ;gDAAkC;gDACnD,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gDACjC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gDACjC;;;;;;0DAIF,8OAAC;gDAAI,WAAU;;;;;;;uCAtBV;;;;;;;;;;;;;;;;kCA6Bb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;8CAEhD,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;uCAEe", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo, useState } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\ntype GalleryImage = {\r\n  src: string;\r\n  alt: string;\r\n  category: string;\r\n  description: string;\r\n  featured?: boolean;\r\n};\r\n\r\n// Enhanced gallery with categories and descriptions\r\nconst sampleImages: GalleryImage[] = [\r\n  {\r\n    src: \"/Picture2.jpg\",\r\n    alt: \"HVAC installation - exterior unit\",\r\n    category: \"Installation\",\r\n    description: \"Professional exterior unit installation with proper ventilation and safety protocols.\",\r\n    featured: true\r\n  },\r\n  {\r\n    src: \"/Picture4.jpg\",\r\n    alt: \"Ductwork and air handling system\",\r\n    category: \"Ductwork\",\r\n    description: \"Precision ductwork installation ensuring optimal airflow and energy efficiency.\"\r\n  },\r\n  {\r\n    src: \"/Picture5.jpg\",\r\n    alt: \"Thermostat and control wiring\",\r\n    category: \"Smart Controls\",\r\n    description: \"Advanced thermostat installation with smart home integration capabilities.\"\r\n  },\r\n  {\r\n    src: \"/Picture6.jpg\",\r\n    alt: \"Clean indoor unit installation\",\r\n    category: \"Installation\",\r\n    description: \"Clean and efficient indoor unit setup with minimal disruption to your space.\",\r\n    featured: true\r\n  },\r\n  {\r\n    src: \"/Picture8.jpg\",\r\n    alt: \"Technician onsite maintenance\",\r\n    category: \"Maintenance\",\r\n    description: \"Regular maintenance service ensuring peak performance and longevity.\"\r\n  },\r\n  {\r\n    src: \"/Picture11.jpg\",\r\n    alt: \"Smart climate setup overview\",\r\n    category: \"Smart Controls\",\r\n    description: \"Complete smart climate control system with automated scheduling and monitoring.\",\r\n    featured: true\r\n  },\r\n];\r\n\r\nexport default function Gallery() {\r\n  const images = useMemo(() => sampleImages, []);\r\n  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);\r\n  const [filter, setFilter] = useState<string>(\"All\");\r\n\r\n  const categories = [\"All\", ...Array.from(new Set(images.map(img => img.category)))];\r\n  const filteredImages = filter === \"All\" ? images : images.filter(img => img.category === filter);\r\n\r\n  return (\r\n    <section className=\"relative py-20 sm:py-24 bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50 overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0 opacity-20\">\r\n        <div className=\"absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-400/20 to-teal-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        {/* Enhanced Header */}\r\n        <motion.header\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-16\"\r\n        >\r\n          <div className=\"inline-flex items-center gap-3 px-8 py-4 rounded-full bg-gradient-to-r from-blue-100 via-cyan-100 to-emerald-100 text-blue-800 text-sm font-bold mb-8 shadow-lg\">\r\n            <span className=\"w-3 h-3 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full animate-pulse\"></span>\r\n            Project Gallery\r\n            <span className=\"w-3 h-3 bg-gradient-to-r from-cyan-600 to-emerald-600 rounded-full animate-pulse delay-500\"></span>\r\n          </div>\r\n          <h2 className=\"text-5xl md:text-6xl font-bold mb-6 leading-tight\">\r\n            <span className=\"text-gray-900\">Our</span>{' '}\r\n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-cyan-600 to-emerald-600\">\r\n              HVAC Excellence\r\n            </span>\r\n          </h2>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            Explore our recent installations, maintenance work, and smart climate solutions.\r\n            Each project showcases our commitment to quality and innovation.\r\n          </p>\r\n        </motion.header>\r\n\r\n        {/* Category Filter */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6, delay: 0.2 }}\r\n          viewport={{ once: true }}\r\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\r\n        >\r\n          {categories.map((category) => (\r\n            <button\r\n              key={category}\r\n              onClick={() => setFilter(category)}\r\n              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\r\n                filter === category\r\n                  ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg scale-105\"\r\n                  : \"bg-white/80 text-gray-700 hover:bg-white hover:shadow-md hover:scale-105\"\r\n              }`}\r\n            >\r\n              {category}\r\n            </button>\r\n          ))}\r\n        </motion.div>\r\n\r\n        {/* Enhanced Gallery Grid */}\r\n        <motion.div\r\n          layout\r\n          className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\"\r\n        >\r\n          <AnimatePresence>\r\n            {filteredImages.map((image, index) => (\r\n              <motion.div\r\n                key={image.src}\r\n                layout\r\n                initial={{ opacity: 0, scale: 0.8 }}\r\n                animate={{ opacity: 1, scale: 1 }}\r\n                exit={{ opacity: 0, scale: 0.8 }}\r\n                transition={{ duration: 0.5, delay: index * 0.1 }}\r\n                className={`group relative cursor-pointer ${\r\n                  image.featured ? \"lg:col-span-2 lg:row-span-2\" : \"\"\r\n                }`}\r\n                onClick={() => setSelectedImage(image)}\r\n              >\r\n                <div className=\"relative overflow-hidden rounded-3xl bg-white/80 backdrop-blur-sm shadow-xl border border-white/30 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2\">\r\n                  <div className={`relative ${image.featured ? \"aspect-[16/10]\" : \"aspect-[4/3]\"} overflow-hidden`}>\r\n                    <img\r\n                      src={image.src}\r\n                      alt={image.alt}\r\n                      loading=\"lazy\"\r\n                      className=\"h-full w-full object-cover transition duration-500 group-hover:scale-110\"\r\n                    />\r\n\r\n                    {/* Overlay */}\r\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\" />\r\n\r\n                    {/* Category Badge */}\r\n                    <div className=\"absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-blue-600 to-cyan-600 text-white text-sm font-semibold rounded-full\">\r\n                      {image.category}\r\n                    </div>\r\n\r\n                    {/* Content */}\r\n                    <div className=\"absolute inset-x-0 bottom-0 p-6 text-white transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500\">\r\n                      <h3 className=\"text-xl font-bold mb-2\">{image.alt}</h3>\r\n                      <p className=\"text-sm text-white/90 leading-relaxed\">{image.description}</p>\r\n                    </div>\r\n\r\n                    {/* View Icon */}\r\n                    <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 scale-75 group-hover:scale-100\">\r\n                      <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            ))}\r\n          </AnimatePresence>\r\n        </motion.div>\r\n\r\n        {/* Lightbox Modal */}\r\n        <AnimatePresence>\r\n          {selectedImage && (\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              exit={{ opacity: 0 }}\r\n              className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm p-4\"\r\n              onClick={() => setSelectedImage(null)}\r\n            >\r\n              <motion.div\r\n                initial={{ scale: 0.8, opacity: 0 }}\r\n                animate={{ scale: 1, opacity: 1 }}\r\n                exit={{ scale: 0.8, opacity: 0 }}\r\n                className=\"relative max-w-4xl max-h-[90vh] bg-white rounded-3xl overflow-hidden shadow-2xl\"\r\n                onClick={(e) => e.stopPropagation()}\r\n              >\r\n                <div className=\"relative\">\r\n                  <img\r\n                    src={selectedImage.src}\r\n                    alt={selectedImage.alt}\r\n                    className=\"w-full h-auto max-h-[70vh] object-cover\"\r\n                  />\r\n\r\n                  {/* Close Button */}\r\n                  <button\r\n                    onClick={() => setSelectedImage(null)}\r\n                    className=\"absolute top-4 right-4 w-10 h-10 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors duration-300\"\r\n                  >\r\n                    <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n                    </svg>\r\n                  </button>\r\n\r\n                  {/* Category Badge */}\r\n                  <div className=\"absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-blue-600 to-cyan-600 text-white text-sm font-semibold rounded-full\">\r\n                    {selectedImage.category}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Content */}\r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{selectedImage.alt}</h3>\r\n                  <p className=\"text-gray-600 leading-relaxed\">{selectedImage.description}</p>\r\n                </div>\r\n              </motion.div>\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAHA;;;;AAaA,oDAAoD;AACpD,MAAM,eAA+B;IACnC;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;IACZ;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;IACf;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;IACf;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;IACZ;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;IACf;IACA;QACE,KAAK;QACL,KAAK;QACL,UAAU;QACV,aAAa;QACb,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,SAAS,IAAA,gNAAO,EAAC,IAAM,cAAc,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,IAAA,iNAAQ,EAAsB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAAS;IAE7C,MAAM,aAAa;QAAC;WAAU,MAAM,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;KAAI;IACnF,MAAM,iBAAiB,WAAW,QAAQ,SAAS,OAAO,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,KAAK;IAEzF,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,MAAM;wBACZ,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;oCAAuF;kDAEvG,8OAAC;wCAAK,WAAU;;;;;;;;;;;;0CAElB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAW;kDAC3C,8OAAC;wCAAK,WAAU;kDAA2F;;;;;;;;;;;;0CAI7G,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAET,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gCAEC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,iEAAiE,EAC3E,WAAW,WACP,8EACA,4EACJ;0CAED;+BARI;;;;;;;;;;kCAcX,8OAAC,oMAAM,CAAC,GAAG;wBACT,MAAM;wBACN,WAAU;kCAEV,cAAA,8OAAC,4MAAe;sCACb,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,oMAAM,CAAC,GAAG;oCAET,MAAM;oCACN,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,MAAM;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAC/B,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,WAAW,CAAC,8BAA8B,EACxC,MAAM,QAAQ,GAAG,gCAAgC,IACjD;oCACF,SAAS,IAAM,iBAAiB;8CAEhC,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,CAAC,SAAS,EAAE,MAAM,QAAQ,GAAG,mBAAmB,eAAe,gBAAgB,CAAC;;8DAC9F,8OAAC;oDACC,KAAK,MAAM,GAAG;oDACd,KAAK,MAAM,GAAG;oDACd,SAAQ;oDACR,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDAAI,WAAU;8DACZ,MAAM,QAAQ;;;;;;8DAIjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAA0B,MAAM,GAAG;;;;;;sEACjD,8OAAC;4DAAE,WAAU;sEAAyC,MAAM,WAAW;;;;;;;;;;;;8DAIzE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;;0EAC5E,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAtCxE,MAAM,GAAG;;;;;;;;;;;;;;;kCAiDtB,8OAAC,4MAAe;kCACb,+BACC,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,WAAU;4BACV,SAAS,IAAM,iBAAiB;sCAEhC,cAAA,8OAAC,oMAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAClC,SAAS;oCAAE,OAAO;oCAAG,SAAS;gCAAE;gCAChC,MAAM;oCAAE,OAAO;oCAAK,SAAS;gCAAE;gCAC/B,WAAU;gCACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kDAEjC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,cAAc,GAAG;gDACtB,KAAK,cAAc,GAAG;gDACtB,WAAU;;;;;;0DAIZ,8OAAC;gDACC,SAAS,IAAM,iBAAiB;gDAChC,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAKzE,8OAAC;gDAAI,WAAU;0DACZ,cAAc,QAAQ;;;;;;;;;;;;kDAK3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC,cAAc,GAAG;;;;;;0DACxE,8OAAC;gDAAE,WAAU;0DAAiC,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzF", "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from 'next/link'\r\nimport Image from 'next/image'\r\nimport React from 'react'\r\nimport { motion } from 'framer-motion'\r\n\r\nconst AboutTeaser = () => {\r\n  const features = [\r\n    {\r\n      title: \"Premium Quality Service\",\r\n      description: \"Industry-leading HVAC solutions with certified technicians and cutting-edge equipment for optimal performance.\",\r\n      icon: (\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full blur-sm opacity-75\"></div>\r\n          <svg className=\"relative w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\r\n          </svg>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      title: \"Comprehensive Warranty\",\r\n      description: \"Extended warranty coverage with 24/7 maintenance support ensuring your systems run efficiently year-round.\",\r\n      icon: (\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full blur-sm opacity-75\"></div>\r\n          <svg className=\"relative w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z\"/>\r\n          </svg>\r\n        </div>\r\n      )\r\n    },\r\n    {\r\n      title: \"Energy Efficiency\",\r\n      description: \"Smart HVAC solutions that reduce energy consumption by up to 40% while maintaining optimal comfort levels.\",\r\n      icon: (\r\n        <div className=\"relative\">\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full blur-sm opacity-75\"></div>\r\n          <svg className=\"relative w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path d=\"M13 2L3 14h9l-1 8 10-12h-9l1-8z\"/>\r\n          </svg>\r\n        </div>\r\n      )\r\n    }\r\n  ];\r\n\r\n  const stats = [\r\n    { number: \"8+\", label: \"Years Experience\", icon: \"🏆\" },\r\n    { number: \"150+\", label: \"Projects Completed\", icon: \"🔧\" },\r\n    { number: \"100%\", label: \"Client Satisfaction\", icon: \"⭐\" },\r\n    { number: \"24/7\", label: \"Support Available\", icon: \"🕒\" }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative py-20 lg:py-28 bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50 overflow-hidden\">\r\n      {/* Background Elements */}\r\n      <div className=\"absolute inset-0 opacity-40\">\r\n        <div className=\"absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-400/20 to-teal-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n        {/* Main Content Grid */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 items-center\">\r\n          {/* Left Content - Enhanced Images */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: -50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"relative\"\r\n          >\r\n            {/* Main Image with Enhanced Design */}\r\n            <div className=\"relative\">\r\n              <div className=\"relative w-4/5 h-[500px] lg:h-[600px] rounded-3xl overflow-hidden shadow-2xl transform rotate-2 hover:rotate-0 transition-transform duration-500\">\r\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent z-10\"></div>\r\n                <Image\r\n                  src=\"/about1.jpg\"\r\n                  alt=\"HVAC Technician Working\"\r\n                  fill\r\n                  className=\"object-cover\"\r\n                  sizes=\"(max-width: 768px) 100vw, 50vw\"\r\n                  priority\r\n                />\r\n              </div>\r\n\r\n              {/* Floating Stats Card */}\r\n              <div className=\"absolute -bottom-8 -right-8 bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-white/20\">\r\n                <div className=\"grid grid-cols-2 gap-4\">\r\n                  {stats.slice(0, 2).map((stat, index) => (\r\n                    <div key={index} className=\"text-center\">\r\n                      <div className=\"text-2xl mb-1\">{stat.icon}</div>\r\n                      <div className=\"text-2xl font-bold text-gray-900\">{stat.number}</div>\r\n                      <div className=\"text-xs text-gray-600\">{stat.label}</div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Secondary Image with Modern Design */}\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.6, delay: 0.5 }}\r\n              viewport={{ once: true }}\r\n              className=\"absolute top-20 -right-8 w-1/2 h-[280px] lg:h-[320px] rounded-2xl overflow-hidden shadow-xl transform -rotate-3 hover:rotate-0 transition-transform duration-500\"\r\n            >\r\n              <div className=\"absolute inset-0 bg-gradient-to-t from-blue-600/30 to-transparent z-10\"></div>\r\n              <Image\r\n                src=\"/about2.jpg\"\r\n                alt=\"HVAC System Installation\"\r\n                fill\r\n                className=\"object-cover\"\r\n                sizes=\"(max-width: 768px) 50vw, 25vw\"\r\n              />\r\n\r\n              {/* Quality Badge */}\r\n              <div className=\"absolute top-4 left-4 bg-emerald-500 text-white px-3 py-1 rounded-full text-sm font-semibold z-20\">\r\n                Certified\r\n              </div>\r\n            </motion.div>\r\n\r\n            {/* Decorative Elements */}\r\n            <div className=\"absolute -bottom-8 -left-8 w-32 h-32 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 rounded-full blur-xl\"></div>\r\n            <div className=\"absolute -top-8 right-1/4 w-20 h-20 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-full blur-lg\"></div>\r\n          </motion.div>\r\n\r\n          {/* Right Content */}\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 50 }}\r\n            whileInView={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.8 }}\r\n            viewport={{ once: true }}\r\n            className=\"space-y-10\"\r\n          >\r\n            {/* Section Label with Modern Design */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6 }}\r\n              viewport={{ once: true }}\r\n              className=\"inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 text-sm font-semibold\"\r\n            >\r\n              <span className=\"w-2 h-2 bg-blue-600 rounded-full animate-pulse\"></span>\r\n              About Our Company\r\n            </motion.div>\r\n\r\n            {/* Main Heading with Gradient */}\r\n            <motion.h2\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight\"\r\n            >\r\n              <span className=\"text-gray-900\">Your Trusted</span>{' '}\r\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-cyan-600 to-emerald-600\">\r\n                HVAC Excellence Partner\r\n              </span>\r\n            </motion.h2>\r\n\r\n            {/* Enhanced Description */}\r\n            <motion.p\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.2 }}\r\n              viewport={{ once: true }}\r\n              className=\"text-lg text-gray-600 leading-relaxed max-w-xl\"\r\n            >\r\n              We deliver cutting-edge HVAC solutions with unmatched expertise and reliability.\r\n              Our certified technicians ensure optimal comfort and energy efficiency for your space.\r\n            </motion.p>\r\n\r\n            {/* Stats Grid */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.3 }}\r\n              viewport={{ once: true }}\r\n              className=\"grid grid-cols-2 gap-6 py-6\"\r\n            >\r\n              {stats.slice(2).map((stat, index) => (\r\n                <div key={index} className=\"text-center p-4 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20\">\r\n                  <div className=\"text-3xl mb-2\">{stat.icon}</div>\r\n                  <div className=\"text-3xl font-bold text-gray-900 mb-1\">{stat.number}</div>\r\n                  <div className=\"text-sm text-gray-600\">{stat.label}</div>\r\n                </div>\r\n              ))}\r\n            </motion.div>\r\n\r\n            {/* Enhanced Features */}\r\n            <div className=\"space-y-6\">\r\n              {features.map((feature, index) => (\r\n                <motion.div\r\n                  key={index}\r\n                  initial={{ opacity: 0, y: 20 }}\r\n                  whileInView={{ opacity: 1, y: 0 }}\r\n                  transition={{ duration: 0.5, delay: 0.4 + index * 0.1 }}\r\n                  viewport={{ once: true }}\r\n                  className=\"group flex items-start gap-6 p-6 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20 hover:bg-white/80 hover:shadow-lg transition-all duration-300\"\r\n                >\r\n                  <div className=\"flex-shrink-0 p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl group-hover:scale-110 transition-transform duration-300\">\r\n                    {feature.icon}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h4 className=\"text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300\">\r\n                      {feature.title}\r\n                    </h4>\r\n                    <p className=\"text-gray-600 leading-relaxed\">\r\n                      {feature.description}\r\n                    </p>\r\n                  </div>\r\n                </motion.div>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Enhanced CTA Button */}\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 20 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: 0.7 }}\r\n              viewport={{ once: true }}\r\n              className=\"pt-6\"\r\n            >\r\n              <Link\r\n                href=\"/about\"\r\n                className=\"group inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-blue-600 to-cyan-600 text-white font-semibold rounded-2xl hover:from-blue-700 hover:to-cyan-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl\"\r\n              >\r\n                Discover More About Us\r\n                <svg className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\r\n                </svg>\r\n              </Link>\r\n            </motion.div>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  )\r\n}\r\n\r\nexport default AboutTeaser\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,cAAc;IAClB,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA8B,MAAK;wBAAe,SAAQ;kCACvE,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;QAIhB;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA8B,MAAK;wBAAe,SAAQ;kCACvE,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;QAIhB;QACA;YACE,OAAO;YACP,aAAa;YACb,oBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA8B,MAAK;wBAAe,SAAQ;kCACvE,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;QAIhB;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAM,OAAO;YAAoB,MAAM;QAAK;QACtD;YAAE,QAAQ;YAAQ,OAAO;YAAsB,MAAM;QAAK;QAC1D;YAAE,QAAQ;YAAQ,OAAO;YAAuB,MAAM;QAAI;QAC1D;YAAE,QAAQ;YAAQ,OAAO;YAAqB,MAAM;QAAK;KAC1D;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC,wIAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;oDACV,OAAM;oDACN,QAAQ;;;;;;;;;;;;sDAKZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAI,WAAU;0EAAiB,KAAK,IAAI;;;;;;0EACzC,8OAAC;gEAAI,WAAU;0EAAoC,KAAK,MAAM;;;;;;0EAC9D,8OAAC;gEAAI,WAAU;0EAAyB,KAAK,KAAK;;;;;;;uDAH1C;;;;;;;;;;;;;;;;;;;;;8CAWlB,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,wIAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,OAAM;;;;;;sDAIR,8OAAC;4CAAI,WAAU;sDAAoG;;;;;;;;;;;;8CAMrH,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,8OAAC,oMAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCAAwD;;;;;;;8CAK1E,8OAAC,oMAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAAoB;sDACpD,8OAAC;4CAAK,WAAU;sDAAiG;;;;;;;;;;;;8CAMnH,8OAAC,oMAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CACX;;;;;;8CAMD,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAET,MAAM,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;8DAAiB,KAAK,IAAI;;;;;;8DACzC,8OAAC;oDAAI,WAAU;8DAAyC,KAAK,MAAM;;;;;;8DACnE,8OAAC;oDAAI,WAAU;8DAAyB,KAAK,KAAK;;;;;;;2CAH1C;;;;;;;;;;8CASd,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,oMAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,IAAI;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;;2CAfnB;;;;;;;;;;8CAuBX,8OAAC,oMAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,8OAAC,uKAAI;wCACH,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC;gDAAI,WAAU;gDAAsE,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC7H,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvF;uCAEe", "debugId": null}}, {"offset": {"line": 2036, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/HVAC/my-app/src/components/home/<USER>"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { motion } from 'framer-motion';\r\n\r\nexport function WhyChooseUsSection() {\r\n  const reasons = [\r\n    {\r\n      icon: \"⚡\",\r\n      title: \"Lightning-Fast Response\",\r\n      description: \"Our 24/7 emergency response team ensures your HVAC issues are resolved within hours, not days. Advanced dispatch system guarantees rapid deployment.\",\r\n      highlight: \"< 2 Hour Response\",\r\n      color: \"from-amber-500 to-orange-500\",\r\n      bgColor: \"from-amber-50 to-orange-50\"\r\n    },\r\n    {\r\n      icon: \"🔧\",\r\n      title: \"Advanced HVAC Technology\",\r\n      description: \"Proprietary diagnostic tools and cutting-edge repair methods reduce downtime by 60% while ensuring long-lasting solutions.\",\r\n      highlight: \"60% Faster Repairs\",\r\n      color: \"from-blue-500 to-cyan-500\",\r\n      bgColor: \"from-blue-50 to-cyan-50\"\r\n    },\r\n    {\r\n      icon: \"👨‍🔧\",\r\n      title: \"Certified Specialists\",\r\n      description: \"EPA-certified technicians with 10+ years experience and continuous training on latest HVAC technologies and safety protocols.\",\r\n      highlight: \"EPA Certified Team\",\r\n      color: \"from-emerald-500 to-teal-500\",\r\n      bgColor: \"from-emerald-50 to-teal-50\"\r\n    },\r\n    {\r\n      icon: \"🛡️\",\r\n      title: \"Comprehensive Warranty\",\r\n      description: \"Industry-leading warranty coverage with 100% satisfaction guarantee. All parts and labor protected for complete peace of mind.\",\r\n      highlight: \"100% Satisfaction\",\r\n      color: \"from-purple-500 to-indigo-500\",\r\n      bgColor: \"from-purple-50 to-indigo-50\"\r\n    }\r\n  ];\r\n\r\n  const specialties = [\r\n    {\r\n      icon: \"🏭\",\r\n      title: \"Industrial HVAC\",\r\n      description: \"Manufacturing solutions\",\r\n      color: \"from-gray-600 to-slate-600\"\r\n    },\r\n    {\r\n      icon: \"🏢\",\r\n      title: \"Commercial Systems\",\r\n      description: \"Office & retail HVAC\",\r\n      color: \"from-blue-500 to-indigo-500\"\r\n    },\r\n    {\r\n      icon: \"⚙️\",\r\n      title: \"Smart Controls\",\r\n      description: \"Intelligent automation\",\r\n      color: \"from-purple-500 to-indigo-500\"\r\n    },\r\n    {\r\n      icon: \"🔧\",\r\n      title: \"Maintenance\",\r\n      description: \"Preventive care programs\",\r\n      color: \"from-emerald-500 to-teal-500\"\r\n    },\r\n    {\r\n      icon: \"❄️\",\r\n      title: \"Air Conditioning\",\r\n      description: \"Smart cooling solutions\",\r\n      color: \"from-cyan-500 to-blue-500\"\r\n    },\r\n    {\r\n      icon: \"🔥\",\r\n      title: \"Heating Systems\",\r\n      description: \"Efficient heating tech\",\r\n      color: \"from-red-500 to-orange-500\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <section className=\"relative w-full py-24 bg-gradient-to-br from-slate-50 via-blue-50/40 to-slate-50 overflow-hidden\">\r\n      {/* Enhanced Background Elements */}\r\n      <div className=\"absolute inset-0 opacity-30\">\r\n        <div className=\"absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-400/30 to-cyan-400/30 rounded-full mix-blend-multiply filter blur-3xl animate-pulse\"></div>\r\n        <div className=\"absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-purple-400/30 to-indigo-400/30 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000\"></div>\r\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-to-r from-emerald-400/20 to-teal-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-2000\"></div>\r\n      </div>\r\n\r\n      <div className=\"relative container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl\">\r\n        {/* Enhanced Section Header */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-20\"\r\n        >\r\n          <div className=\"inline-flex items-center gap-3 px-8 py-4 rounded-full bg-gradient-to-r from-blue-100 via-cyan-100 to-emerald-100 text-blue-800 text-sm font-bold mb-8 shadow-lg\">\r\n            <span className=\"w-3 h-3 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full animate-pulse\"></span>\r\n            Why Choose Our HVAC Excellence\r\n            <span className=\"w-3 h-3 bg-gradient-to-r from-cyan-600 to-emerald-600 rounded-full animate-pulse delay-500\"></span>\r\n          </div>\r\n          <h2 className=\"text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight\">\r\n            <span className=\"text-gray-900\">Your Trusted</span>{' '}\r\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-cyan-600 to-emerald-600\">\r\n              HVAC Excellence Partner\r\n            </span>\r\n          </h2>\r\n          <p className=\"text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\r\n            Experience unmatched HVAC solutions with our certified experts, cutting-edge technology,\r\n            and commitment to your comfort and satisfaction.\r\n          </p>\r\n        </motion.div>\r\n\r\n        {/* Main Reasons Grid */}\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-10 mb-24\">\r\n          {reasons.map((reason, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, y: 30 }}\r\n              whileInView={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.6, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"group relative bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl border border-white/30 hover:shadow-2xl transition-all duration-500 hover:-translate-y-3 overflow-hidden\"\r\n            >\r\n              {/* Background Gradient */}\r\n              <div className={`absolute inset-0 bg-gradient-to-br ${reason.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}></div>\r\n\r\n              {/* Content */}\r\n              <div className=\"relative z-10\">\r\n                <div className=\"flex items-start gap-6 mb-6\">\r\n                  <div className={`p-4 bg-gradient-to-r ${reason.color} rounded-2xl text-white text-4xl group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg`}>\r\n                    {reason.icon}\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h3 className=\"text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors duration-300\">\r\n                      {reason.title}\r\n                    </h3>\r\n                    <div className={`inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r ${reason.color} text-white text-sm font-bold rounded-full shadow-md`}>\r\n                      <span className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></span>\r\n                      {reason.highlight}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <p className=\"text-gray-600 leading-relaxed text-lg group-hover:text-gray-700 transition-colors duration-300\">\r\n                  {reason.description}\r\n                </p>\r\n\r\n                {/* Decorative Element */}\r\n                <div className={`absolute -bottom-2 -right-2 w-24 h-24 bg-gradient-to-r ${reason.color} rounded-full opacity-10 group-hover:opacity-20 transition-opacity duration-500`}></div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n\r\n        {/* Specialties Section */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 30 }}\r\n          whileInView={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.6 }}\r\n          viewport={{ once: true }}\r\n          className=\"text-center mb-12\"\r\n        >\r\n          <div className=\"inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-800 text-sm font-bold mb-6\">\r\n            <span className=\"w-2 h-2 bg-emerald-600 rounded-full animate-pulse\"></span>\r\n            Our Core Specialties\r\n          </div>\r\n          <h3 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\r\n            Complete <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600\">HVAC Solutions</span>\r\n          </h3>\r\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\r\n            From industrial installations to smart home automation, we deliver comprehensive HVAC solutions\r\n            tailored to your specific needs and industry requirements.\r\n          </p>\r\n        </motion.div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n          {specialties.map((specialty, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              whileInView={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.5, delay: index * 0.1 }}\r\n              viewport={{ once: true }}\r\n              className=\"group relative text-center p-8 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/30 hover:bg-white/90 transition-all duration-500 hover:shadow-xl hover:-translate-y-2 overflow-hidden\"\r\n            >\r\n              {/* Background Gradient */}\r\n              <div className={`absolute inset-0 bg-gradient-to-br ${specialty.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>\r\n\r\n              {/* Content */}\r\n              <div className=\"relative z-10\">\r\n                <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r ${specialty.color} rounded-2xl text-white text-3xl mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg`}>\r\n                  {specialty.icon}\r\n                </div>\r\n                <h4 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-gray-800 transition-colors duration-300\">\r\n                  {specialty.title}\r\n                </h4>\r\n                <p className=\"text-gray-600 group-hover:text-gray-700 transition-colors duration-300\">\r\n                  {specialty.description}\r\n                </p>\r\n\r\n                {/* Decorative Element */}\r\n                <div className={`absolute -bottom-4 -right-4 w-20 h-20 bg-gradient-to-r ${specialty.color} rounded-full opacity-5 group-hover:opacity-15 transition-opacity duration-500`}></div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAKO,SAAS;IACd,MAAM,UAAU;QACd;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;YACX,OAAO;YACP,SAAS;QACX;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,WAAW;YACX,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YAC<PERSON>,aAAa;YAC<PERSON>,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;oCAAuF;kDAEvG,8OAAC;wCAAK,WAAU;;;;;;;;;;;;0CAElB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;oCAAoB;kDACpD,8OAAC;wCAAK,WAAU;kDAAiG;;;;;;;;;;;;0CAInH,8OAAC;gCAAE,WAAU;0CAAsE;;;;;;;;;;;;kCAOrF,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,oMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAGV,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,OAAO,OAAO,CAAC,kEAAkE,CAAC;;;;;;kDAGxI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EAAE,OAAO,KAAK,CAAC,iHAAiH,CAAC;kEACpK,OAAO,IAAI;;;;;;kEAEd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,OAAO,KAAK;;;;;;0EAEf,8OAAC;gEAAI,WAAW,CAAC,0DAA0D,EAAE,OAAO,KAAK,CAAC,oDAAoD,CAAC;;kFAC7I,8OAAC;wEAAK,WAAU;;;;;;oEACf,OAAO,SAAS;;;;;;;;;;;;;;;;;;;0DAKvB,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;0DAIrB,8OAAC;gDAAI,WAAW,CAAC,uDAAuD,EAAE,OAAO,KAAK,CAAC,+EAA+E,CAAC;;;;;;;;;;;;;+BAhCpK;;;;;;;;;;kCAuCX,8OAAC,oMAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;;;;;oCAA2D;;;;;;;0CAG7E,8OAAC;gCAAG,WAAU;;oCAAoD;kDACvD,8OAAC;wCAAK,WAAU;kDAA8E;;;;;;;;;;;;0CAEzG,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,WAAW,sBAC3B,8OAAC,oMAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAGV,8OAAC;wCAAI,WAAW,CAAC,mCAAmC,EAAE,UAAU,KAAK,CAAC,iEAAiE,CAAC;;;;;;kDAGxI,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAC,mEAAmE,EAAE,UAAU,KAAK,CAAC,sHAAsH,CAAC;0DAC1N,UAAU,IAAI;;;;;;0DAEjB,8OAAC;gDAAG,WAAU;0DACX,UAAU,KAAK;;;;;;0DAElB,8OAAC;gDAAE,WAAU;0DACV,UAAU,WAAW;;;;;;0DAIxB,8OAAC;gDAAI,WAAW,CAAC,uDAAuD,EAAE,UAAU,KAAK,CAAC,8EAA8E,CAAC;;;;;;;;;;;;;+BAvBtK;;;;;;;;;;;;;;;;;;;;;;AA+BnB", "debugId": null}}]}