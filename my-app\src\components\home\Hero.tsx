"use client";

import React, { useState, useEffect } from "react";
import { TypewriterEffectSmooth } from "@/components/ui/typewriter-effect-smooth";
import Image from "next/image";
import Link from "next/link";

const Hero: React.FC = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentBg, setCurrentBg] = useState(0);

  // Typewriter animation words
  const words = [
    { text: "Breathe", className: "text-white font-bold" },
    { text: "Easy...", className: "text-blue-400 font-bold" },
    { text: "Live", className: "text-white font-bold" },
    { text: "Easy...", className: "text-cyan-400 font-bold" },
  ];

  // Background rotation
  const backgrounds = ["/background.jpg", "/background1.jpg", "/about1.jpg"];

  useEffect(() => {
    setIsLoaded(true);
    const bgInterval = setInterval(() => {
      setCurrentBg((prev) => (prev + 1) % backgrounds.length);
    }, 8000);
    return () => clearInterval(bgInterval);
  }, [backgrounds.length]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with Parallax Effect */}
      <div className="absolute inset-0">
        {backgrounds.map((bg, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-all duration-2000 ${
              index === currentBg
                ? "opacity-100 scale-100"
                : "opacity-0 scale-105"
            }`}
          >
            <Image
              src={bg}
              alt="HVAC Background"
              fill
              className="object-cover"
              priority={index === 0}
            />
          </div>
        ))}

        {/* Modern Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900/90 via-blue-900/80 to-slate-900/90" />

        {/* Geometric Pattern Overlay */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-20 w-32 h-32 border border-white rotate-45" />
          <div className="absolute bottom-20 right-20 w-24 h-24 border border-cyan-400 rotate-12" />
          <div className="absolute top-1/2 left-10 w-16 h-16 border border-blue-400 -rotate-12" />
        </div>
      </div>

      {/* Main Content */}
      <div
        className={`relative z-10 w-full max-w-6xl mx-auto px-6 transition-all duration-1000 ${
          isLoaded ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
        }`}
      >
        {/* Hero Grid Layout */}
        <div className="grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]">
          {/* Left Column - Content */}
          <div className="text-white space-y-8">
            {/* Main Heading */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
              <span className="text-white">ST HVAC</span>
              <br />
              <span className="text-transparent bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text">
                SALES & SERVICES
              </span>
            </h1>

            {/* Typewriter */}
            <div className="text-xl sm:text-2xl">
              <TypewriterEffectSmooth
                words={words}
                className="text-xl sm:text-2xl lg:text-3xl"
                cursorClassName="bg-cyan-400"
              />
            </div>

            {/* Description */}
            <p className="text-lg text-gray-300 leading-relaxed max-w-lg">
              Expert heating, cooling, and air quality solutions. From emergency
              repairs to complete system installations, we deliver reliable
              comfort for your home and business.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="/about"
                className="px-8 py-4 bg-[#18dbc8] hover:bg-[#18dbc8] text-white rounded-lg font-semibold transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Learn More
              </Link>
            </div>
          </div>

          {/* Right Column - Service Cards */}
          <div className="grid grid-cols-2 gap-6">
            {/* Service Card 1 */}
            <div className="group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-3xl mb-4">❄️</div>
              <h3 className="text-white font-semibold mb-2">
                Air Conditioning
              </h3>
              <p className="text-gray-300 text-sm">
                Installation, repair & maintenance
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-3xl mb-4">🔥</div>
              <h3 className="text-white font-semibold mb-2">Heating Systems</h3>
              <p className="text-gray-300 text-sm">
                Furnaces, heat pumps & boilers
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-3xl mb-4">🌬️</div>
              <h3 className="text-white font-semibold mb-2">Air Quality</h3>
              <p className="text-gray-300 text-sm">
                Filtration & ventilation systems
              </p>
            </div>

            {/* Service Card 4 */}
            <div className="group p-6 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:-translate-y-2">
              <div className="text-3xl mb-4">🔧</div>
              <h3 className="text-white font-semibold mb-2">Maintenance</h3>
              <p className="text-gray-300 text-sm">
                Preventive care & tune-ups
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Background Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
        {backgrounds.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentBg(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentBg
                ? "bg-cyan-400 w-8"
                : "bg-white/40 hover:bg-white/60"
            }`}
          />
        ))}
      </div>
    </section>
  );
};

export default Hero;
