'use client'

import React from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'

const Client = () => {
  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)

  // Duplicate the array to create seamless loop
  const duplicatedImages = [...clientImages, ...clientImages]

  // Professional client statistics
  const clientStats = [
    { number: "29+", label: "Trusted Partners" },
    { number: "500+", label: "Projects Completed" },
    { number: "98%", label: "Client Retention Rate" },
    { number: "8+", label: "Years of Experience" }
  ];

  // Client testimonials
  const testimonials = [
    {
      quote: "Exceptional service and professional installation. Our energy costs have decreased by 25% since the new HVAC system was installed.",
      author: "<PERSON>",
      company: "Metro Office Complex",
      role: "Facility Manager"
    },
    {
      quote: "Reliable, efficient, and always available when we need them. Their maintenance program has kept our systems running smoothly.",
      author: "<PERSON>",
      company: "TechStart Industries",
      role: "Operations Director"
    }
  ];

  return (
    <section className="py-16 lg:py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Professional Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center space-x-2 mb-6">
            <div className="w-12 h-px bg-blue-600"></div>
            <span className="text-blue-600 font-medium text-sm uppercase tracking-wider">Our Clients</span>
            <div className="w-12 h-px bg-blue-600"></div>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Trusted by Industry Leaders
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We are proud to serve a diverse range of clients, from residential homeowners to large commercial
            and industrial facilities, delivering reliable HVAC solutions that exceed expectations.
          </p>
        </motion.div>

        {/* Professional Client Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {clientStats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              viewport={{ once: true }}
              className="text-center p-6 bg-white rounded-lg shadow-md border border-gray-100"
            >
              <div className="text-3xl font-bold text-blue-600 mb-1">{stat.number}</div>
              <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Professional Client Testimonials */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="grid md:grid-cols-2 gap-8 mb-16"
        >
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
              <div className="flex items-start mb-4">
                <svg className="w-8 h-8 text-blue-600 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                </svg>
                <div>
                  <p className="text-gray-700 leading-relaxed mb-4">"{testimonial.quote}"</p>
                  <div>
                    <p className="font-semibold text-gray-900">{testimonial.author}</p>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                    <p className="text-sm text-blue-600 font-medium">{testimonial.company}</p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </motion.div>

        {/* Professional Client Logos Carousel */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="relative overflow-hidden rounded-lg bg-white border border-gray-100 shadow-md p-8"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Trusted by Leading Organizations</h3>
            <p className="text-gray-600">Some of our valued clients and partners</p>
          </div>

          <div className="flex animate-scroll">
            {duplicatedImages.map((image, index) => (
              <div
                key={index}
                className="flex-shrink-0 w-32 h-20 mx-4 relative"
              >
                <div className="relative w-full h-full bg-gray-50 rounded-lg p-3 border border-gray-100">
                  <Image
                    src={`/clients/${image}`}
                    alt={`Client ${(index % clientImages.length) + 1}`}
                    fill
                    className="object-contain filter grayscale hover:grayscale-0 transition-all duration-300 p-2"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Gradient Overlays */}
          <div className="absolute left-0 top-0 w-20 h-full bg-gradient-to-r from-white to-transparent pointer-events-none"></div>
          <div className="absolute right-0 top-0 w-20 h-full bg-gradient-to-l from-white to-transparent pointer-events-none"></div>
        </motion.div>

        {/* Professional Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Experience Professional HVAC Service?
          </h3>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Join our satisfied clients and experience the reliability, quality, and professionalism
            that has made us a trusted HVAC partner.
          </p>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="inline-flex items-center gap-3 px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-300"
          >
            <span>Contact Us Today</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Client