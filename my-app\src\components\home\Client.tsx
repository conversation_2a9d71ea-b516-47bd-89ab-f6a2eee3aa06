'use client'

import React from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'

const Client = () => {
  const clientImages = Array.from({ length: 29 }, (_, i) => `client${i + 1}.jpg`)

  // Duplicate the array to create seamless loop
  const duplicatedImages = [...clientImages, ...clientImages]

  // Client testimonials/stats
  const clientStats = [
    { number: "29+", label: "Trusted Partners", icon: "🤝" },
    { number: "500+", label: "Projects Delivered", icon: "🏗️" },
    { number: "98%", label: "Client Retention", icon: "⭐" },
    { number: "24/7", label: "Support Available", icon: "🕒" }
  ];

  return (
    <section className="relative py-20 bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-10 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-r from-emerald-400/20 to-teal-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center gap-3 px-8 py-4 rounded-full bg-gradient-to-r from-blue-100 via-cyan-100 to-emerald-100 text-blue-800 text-sm font-bold mb-8 shadow-lg">
            <span className="w-3 h-3 bg-gradient-to-r from-blue-600 to-cyan-600 rounded-full animate-pulse"></span>
            Our Valued Partners
            <span className="w-3 h-3 bg-gradient-to-r from-cyan-600 to-emerald-600 rounded-full animate-pulse delay-500"></span>
          </div>
          <h2 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
            <span className="text-gray-900">Trusted by</span>{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-cyan-600 to-emerald-600">
              Industry Leaders
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            We are proud to work with industry leaders and trusted partners who rely on our
            HVAC expertise, quality service, and innovative solutions.
          </p>
        </motion.div>

        {/* Client Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16"
        >
          {clientStats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              viewport={{ once: true }}
              className="text-center p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            >
              <div className="text-4xl mb-3">{stat.icon}</div>
              <div className="text-3xl font-bold text-gray-900 mb-1">{stat.number}</div>
              <div className="text-sm text-gray-600 font-medium">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Carousel Container */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="relative"
        >
          {/* Enhanced Gradient overlays */}
          <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-slate-50 via-slate-50/80 to-transparent z-10"></div>
          <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-slate-50 via-slate-50/80 to-transparent z-10"></div>

          {/* Enhanced Moving Carousel */}
          <div className="flex animate-scroll">
            {duplicatedImages.map((image, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: (index % 29) * 0.05 }}
                className="group flex-shrink-0 mx-6 bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 p-8 flex items-center justify-center w-56 h-40 border border-white/30 hover:border-blue-200 hover:-translate-y-2"
              >
                <Image
                  src={`/${image}`}
                  alt={`Client ${(index % 29) + 1}`}
                  width={140}
                  height={100}
                  className="object-contain max-w-full max-h-full group-hover:scale-110 transition-all duration-500"
                  style={{ filter: 'grayscale(100%) brightness(0.8)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.filter = 'grayscale(0%) brightness(1)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.filter = 'grayscale(100%) brightness(0.8)'
                  }}
                />

                {/* Hover Effect Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-blue-600/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center gap-4 px-8 py-4 bg-white/80 backdrop-blur-sm rounded-full border border-white/30 shadow-lg">
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full animate-pulse"></span>
              <span className="text-gray-700 font-semibold">Trusted by 29+ companies and growing</span>
            </div>
            <div className="w-px h-6 bg-gray-300"></div>
            <div className="flex items-center gap-2">
              <span className="w-3 h-3 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full animate-pulse delay-500"></span>
              <span className="text-gray-700 font-semibold">98% Client Retention Rate</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Client